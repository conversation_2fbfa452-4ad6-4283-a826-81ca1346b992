name: Deploy Telex Frontend - staging

on:
  workflow_dispatch:
  push:
    branches:
      - staging

jobs:
  deploy_telex_fe_staging:
    runs-on: ubuntu-latest
    if: github.event.repository.fork == false

    steps:
      - name: Rebuild frontend (failsafe)
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.TELEX_SERVER_SSH_HOST }}
          username: ${{ secrets.TELEX_SERVER_SSH_USER }}
          password: ${{ secrets.TELEX_SERVER_SSH_PASSWORD }}
          script: |
            cd /var/www/telex_fe_staging
            git reset --hard
            git pull origin staging
            pnpm install
            pnpm build
            
      - name: Pull and Restart App
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.TELEX_SERVER_SSH_HOST }}
          username: ${{ secrets.TELEX_SERVER_SSH_USER }}
          password: ${{ secrets.TELEX_SERVER_SSH_PASSWORD }}
          script: |
            cd /var/www/telex_fe_staging
            supervisorctl restart telexfe_staging
