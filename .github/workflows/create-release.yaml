name: Generate Release JSON

on:
  release:
    types: [published]

jobs:
  create-release-json:
    runs-on: self-hosted
    env:
      minio_endpoint: ${{ vars.MINIO_ENDPOINT }}
      upload_bucket: ${{ vars.MINIO_VERSIONING_BUCKET_NAME }}
      tag_name: ${{ github.event.release.tag_name }}
      source_dir: telexfe
      upload_dir: "/public/versions"
      release_file: release-${{ github.event.release.tag_name }}.json
      version_file: version.json
      release_body: ${{ github.event.release.body }}

    steps:
      - name: Create source directory
        run: |
          rm -rf $source_dir
          mkdir $source_dir

      - name: Create the release JSON file
        run: |
          echo "{" > "$source_dir/$release_file"
          echo "  \"title\": \"$tag_name\"," >> "$source_dir/$release_file"
          echo "  \"body\": \"$release_body\"" >> "$source_dir/$release_file"
          echo "}" >> "$source_dir/$release_file"

      - name: Update version.json
        run: |
          echo "{\"number\":\"$tag_name\",\"release_notes_url\":\"$minio_endpoint/$upload_bucket$upload_dir/$source_dir/$release_file\"}" > $source_dir/$version_file

      - name: Upload to Minio Bucket
        uses: yakubique/minio-upload@v1.1.3
        with:
          endpoint: ${{ vars.MINIO_ENDPOINT }}
          access_key: ${{ secrets.MINIO_ACCESS_KEY }}
          secret_key: ${{ secrets.MINIO_SECRET_KEY }}
          bucket: ${{ env.upload_bucket }}
          source: ${{ env.source_dir }}
          target: ${{ env.upload_dir }}
          recursive: true

      - name: Cleanup source directory
        run: |
          rm -rf $source_dir
