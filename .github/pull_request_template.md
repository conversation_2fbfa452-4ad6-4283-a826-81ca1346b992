<!-- Do not delete this PR template. Just edit it to include the required information -->

# Description

<!-- If your PR fixes an open issue, use `Closes #999` to link your PR with the issue. #999 stands for the issue number you are fixing -->

<!-- Github Issue Example: Closes #31 -->

**Closes #issue_number_here**

# Changes proposed

## What were you told to do?

<!-- Write the title of the issue/feature you are working on -->

## What did you do?

<!-- Talk about the things you did eg. files changes, dependencies installed e.t.c -->

# Check List (Check all the applicable boxes)

🚨Please review the [contribution guideline](CONTRIBUTING.md) for this repository.

<!-- Mark all the applicable boxes. To mark the box as done follow the following conventions -->

<!--
[x] - Correct; marked as done
[X] - Correct; marked as done
[ ] - Correct; marked as **not** done

[] - Not Correct; syntax error
[ x] - Not Correct; space between the brackets
-->

- [ ] My code follows the code style of this project.
- [ ] This PR does not contain plagiarized content.
- [ ] The title and description of the PR is clear and explains the approach.
- [ ] I am making a pull request against the **dev branch** (left side).
- [ ] My commit messages styles matches our requested structure.
- [ ] My code additions will fail neither code linting checks nor unit test.
- [ ] I am only making changes to files I was requested to.

# Screenshots/Videos

<!-- If the changes are static page changes or UI changes add screenshots -->
<!-- If the changes involve implementing a functionality or working with apis, include a video
detailing how to implement the functionality and the request to the api and responses from the api endpoint-->
<!-- Add all the screenshots/videos which support your changes i.e before your change and after your change -->
