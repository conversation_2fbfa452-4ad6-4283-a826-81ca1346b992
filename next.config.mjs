/** @type {import('next').NextConfig} */

const isDev = process.env.NODE_ENV === "development";

const nextConfig = {
  output: "standalone",
  images: {
    domains: [
      "images.unsplash.com",
      "images.stockcake.com",
      "s3-alpha-sig.figma.com",
      "media.staging.telex.im",
      "media.telex.im",
      "lh3.googleusercontent.com",
      "media.tifi.tv",
      "is1-ssl.mzstatic.com",
      'res.cloudinary.com',
      'i.imgur.com'
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "media.tifi.tv",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "**",
      },
    ],
  },
  transpilePackages: ["lucide-react"],
  assetPrefix: isDev ? undefined : "/mainapp",
};

export default nextConfig;
