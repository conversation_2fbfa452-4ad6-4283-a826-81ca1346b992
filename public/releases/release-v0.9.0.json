{
  "title": "v0.9.0",
  "body": "<!-- Release notes generated using configuration in .github/release.yaml at staging -->

## What's Changed
### New Features
* fix:updates on homepage and fixes by @ux-finesse in https://github.com/hngprojects/telex_fe/pull/565
### Other Changes
* Feat/user management by @Explorarthecreator in https://github.com/hngprojects/telex_fe/pull/556
* Feat/FAQ fix by @kingbary in https://github.com/hngprojects/telex_fe/pull/558
* Fix: Privacy Policy and Terms Of Service Navigation On Signup Page by @seshto13 in https://github.com/hngprojects/telex_fe/pull/559
* Fix/blogs by @abdulmalikyusuf in https://github.com/hngprojects/telex_fe/pull/560
* Fix/help center page by @SirhmVFX in https://github.com/hngprojects/telex_fe/pull/562
* Fix/sidebar animation by @SirhmVFX in https://github.com/hngprojects/telex_fe/pull/533
* feat: create database activity monitoring blog article page by @abanicaisse in https://github.com/hngprojects/telex_fe/pull/563
* Blogs/Article 6 by @Ivylegend in https://github.com/hngprojects/telex_fe/pull/564
* Layobright by @Ayodejioladimeji in https://github.com/hngprojects/telex_fe/pull/566
* Fix:Add hero image for the article by @Ivylegend in https://github.com/hngprojects/telex_fe/pull/567
* Fixes/modified dashboard active state by @Justfemi in https://github.com/hngprojects/telex_fe/pull/568
* feat: create second article for database monitoring with telex by @abanicaisse in https://github.com/hngprojects/telex_fe/pull/569
* Enhancement: fixed errors on chat channels by @Ayodejioladimeji in https://github.com/hngprojects/telex_fe/pull/570
* feat: devops monitoring page by @Goketech in https://github.com/hngprojects/telex_fe/pull/571
* bug: fix cors error preventing accept invitation page to load by @abanicaisse in https://github.com/hngprojects/telex_fe/pull/572
* fix:payment-integration by @Lftobs in https://github.com/hngprojects/telex_fe/pull/573
* fix: changed the blog to being fetch from the API by @abdulmalikyusuf in https://github.com/hngprojects/telex_fe/pull/574
* Feat/server monitoring by @kingbary in https://github.com/hngprojects/telex_fe/pull/575
* Feat: Guide to Integrating Telex into Your Tech Stack for Real-Time Updates. by @seshto13 in https://github.com/hngprojects/telex_fe/pull/576
* Feat/cloud monitoring page by @kingbary in https://github.com/hngprojects/telex_fe/pull/577
* feat: application monitoring article by @Goketech in https://github.com/hngprojects/telex_fe/pull/578
* Fix/database monitoring page by @abdulmalikyusuf in https://github.com/hngprojects/telex_fe/pull/580
* chore:enhance-payment-ux by @Lftobs in https://github.com/hngprojects/telex_fe/pull/581
* chore: update navbar dropdown with new design changes by @abanicaisse in https://github.com/hngprojects/telex_fe/pull/582
* Feat/How to setup telex by @Ivylegend in https://github.com/hngprojects/telex_fe/pull/583
* Feature/blog by @maetheartist in https://github.com/hngprojects/telex_fe/pull/584
* Fix: fix article content width and font size. by @JoshuaAkpan in https://github.com/hngprojects/telex_fe/pull/585
* Feat: implement telex article that shows step by step guide for integ… by @JoshuaAkpan in https://github.com/hngprojects/telex_fe/pull/586
* chore: merge changes from dev into staging by @vicradon in https://github.com/hngprojects/telex_fe/pull/587


**Full Changelog**: https://github.com/hngprojects/telex_fe/compare/v0.8.0...v0.9.0"
}
