"use client";
import React, { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  AlertTriangle,
  DollarSign,
  Bot,
  Users,
  Save,
  RefreshCw,
} from "lucide-react";

export interface SpendingLimit {
  id: string;
  type: "ai_calls" | "user_billing";
  limit: number;
  period: "daily" | "weekly" | "monthly";
  currentSpent: number;
  isActive: boolean;
  lastUpdated: string;
}

export interface SpendingLimitsCardProps {
  limits: SpendingLimit[];
  // eslint-disable-next-line no-unused-vars
  onUpdateLimit: (limitId: string, newLimit: number) => void;
  // eslint-disable-next-line no-unused-vars
  onToggleLimit: (limitId: string, isActive: boolean) => void;
  onSave: () => void;
  isLoading?: boolean;
  className?: string;
}

const SpendingLimitsCard: React.FC<SpendingLimitsCardProps> = ({
  limits,
  onUpdateLimit,
  onToggleLimit,
  onSave,
  isLoading = false,
  className = "",
}) => {
  const [editingLimits, setEditingLimits] = useState<Record<string, number>>(
    {}
  );

  const handleLimitChange = (limitId: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setEditingLimits((prev) => ({
      ...prev,
      [limitId]: numValue,
    }));
  };

  const handleUpdateLimit = (limitId: string) => {
    const newLimit = editingLimits[limitId];
    if (newLimit !== undefined) {
      onUpdateLimit(limitId, newLimit);
      setEditingLimits((prev) => {
        const updated = { ...prev };
        delete updated[limitId];
        return updated;
      });
    }
  };

  const getUsagePercentage = (limit: SpendingLimit) => {
    return limit.limit > 0 ? (limit.currentSpent / limit.limit) * 100 : 0;
  };

  const getUsageStatus = (percentage: number) => {
    if (percentage >= 90) return { color: "bg-red-500", status: "Critical" };
    if (percentage >= 75) return { color: "bg-yellow-500", status: "Warning" };
    return { color: "bg-green-500", status: "Normal" };
  };

  const getLimitIcon = (type: string) => {
    return type === "ai_calls" ? (
      <Bot className="w-5 h-5" />
    ) : (
      <Users className="w-5 h-5" />
    );
  };

  const getLimitTitle = (type: string) => {
    return type === "ai_calls" ? "AI API Calls" : "User Billing";
  };

  const getLimitDescription = (type: string) => {
    return type === "ai_calls"
      ? "Maximum amount agents can spend on AI API calls"
      : "Maximum amount agents can bill to users";
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="w-6 h-6 text-blue-500" />
            <div>
              <CardTitle>Global Spending Limits</CardTitle>
              <CardDescription>
                Configure spending thresholds for AI calls and user billing
              </CardDescription>
            </div>
          </div>
          <Button
            onClick={onSave}
            disabled={isLoading}
            className="bg-blue-500 hover:bg-blue-400 text-white"
          >
            {isLoading ? (
              <RefreshCw className="w-4 h-4 animate-spin mr-2" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {limits.map((limit) => {
          const usagePercentage = getUsagePercentage(limit);
          const usageStatus = getUsageStatus(usagePercentage);
          const isEditing = editingLimits[limit.id] !== undefined;
          const currentEditValue = editingLimits[limit.id] ?? limit.limit;

          return (
            <div
              key={limit.id}
              className="border rounded-lg p-4 space-y-4 bg-gray-50"
            >
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getLimitIcon(limit.type)}
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {getLimitTitle(limit.type)}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {getLimitDescription(limit.type)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={limit.isActive ? "default" : "secondary"}
                    className={
                      limit.isActive ? "bg-green-100 text-green-800" : ""
                    }
                  >
                    {limit.isActive ? "Active" : "Inactive"}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={`${usageStatus.color} text-white`}
                  >
                    {usageStatus.status}
                  </Badge>
                </div>
              </div>

              {/* Usage Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    Current Usage: ${limit.currentSpent.toFixed(2)}
                  </span>
                  <span className="text-gray-600">
                    Limit: ${limit.limit.toFixed(2)} / {limit.period}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${usageStatus.color}`}
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>{usagePercentage.toFixed(1)}% used</span>
                  <span>
                    ${(limit.limit - limit.currentSpent).toFixed(2)} remaining
                  </span>
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center gap-4 pt-2 border-t">
                <div className="flex items-center gap-2 flex-1">
                  <label className="text-sm font-medium text-gray-700">
                    Limit ($):
                  </label>
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={currentEditValue}
                    onChange={(e) =>
                      handleLimitChange(limit.id, e.target.value)
                    }
                    className="w-32 focus:border-blue-500"
                    placeholder="0.00"
                  />
                  <span className="text-sm text-gray-500">
                    / {limit.period}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onToggleLimit(limit.id, !limit.isActive)}
                    className={
                      limit.isActive
                        ? "text-red-600 hover:text-red-700"
                        : "text-green-600 hover:text-green-700"
                    }
                  >
                    {limit.isActive ? "Disable" : "Enable"}
                  </Button>

                  {isEditing && (
                    <Button
                      size="sm"
                      onClick={() => handleUpdateLimit(limit.id)}
                      className="bg-blue-500 hover:bg-blue-400 text-white"
                    >
                      Update
                    </Button>
                  )}
                </div>
              </div>

              {/* Warning for high usage */}
              {usagePercentage >= 75 && limit.isActive && (
                <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    {usagePercentage >= 90
                      ? "Critical: Spending limit almost reached!"
                      : "Warning: Approaching spending limit"}
                  </span>
                </div>
              )}

              {/* Last updated */}
              <div className="text-xs text-gray-500">
                Last updated: {new Date(limit.lastUpdated).toLocaleString()}
              </div>
            </div>
          );
        })}

        {/* Summary */}
        <div className="border-t pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                $
                {limits
                  .reduce((sum, limit) => sum + limit.currentSpent, 0)
                  .toFixed(2)}
              </div>
              <div className="text-sm text-blue-800">Total Spent</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                $
                {limits.reduce((sum, limit) => sum + limit.limit, 0).toFixed(2)}
              </div>
              <div className="text-sm text-green-800">Total Limits</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {limits.filter((limit) => limit.isActive).length}/
                {limits.length}
              </div>
              <div className="text-sm text-gray-800">Active Limits</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SpendingLimitsCard;
