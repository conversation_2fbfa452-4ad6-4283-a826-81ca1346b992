"use client";
import React, { useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { X, Plus } from "lucide-react";

export interface NewSpendingLimit {
  type: "ai_calls" | "user_billing";
  limit: number;
  period: "daily" | "weekly" | "monthly";
}

export interface AddSpendingLimitModalProps {
  isOpen: boolean;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onAdd: (limit: NewSpendingLimit) => void;
  isLoading?: boolean;
}

const AddSpendingLimitModal: React.FC<AddSpendingLimitModalProps> = ({
  isOpen,
  onClose,
  onAdd,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<NewSpendingLimit>({
    type: "ai_calls",
    limit: 0,
    period: "daily",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (
    field: keyof NewSpendingLimit,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (formData.limit <= 0) {
      newErrors.limit = "Limit must be greater than 0";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onAdd(formData);
      setFormData({
        type: "ai_calls",
        limit: 0,
        period: "daily",
      });
      setErrors({});
    }
  };

  const handleClose = () => {
    setFormData({
      type: "ai_calls",
      limit: 0,
      period: "daily",
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Add Spending Limit
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Limit Type
            </label>
            <select
              value={formData.type}
              onChange={(e) =>
                handleInputChange(
                  "type",
                  e.target.value as "ai_calls" | "user_billing"
                )
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="ai_calls">AI API Calls</option>
              <option value="user_billing">User Billing</option>
            </select>
          </div>

          {/* Period Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Time Period
            </label>
            <select
              value={formData.period}
              onChange={(e) =>
                handleInputChange(
                  "period",
                  e.target.value as "daily" | "weekly" | "monthly"
                )
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>

          {/* Limit Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spending Limit ($)
            </label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={formData.limit}
              onChange={(e) =>
                handleInputChange("limit", parseFloat(e.target.value) || 0)
              }
              className={`focus:border-blue-500 ${errors.limit ? "border-red-500" : ""}`}
              placeholder="0.00"
            />
            {errors.limit && (
              <p className="text-red-500 text-sm mt-1">{errors.limit}</p>
            )}
          </div>

          {/* Description */}
          <div className="bg-blue-50 p-3 rounded-md">
            <p className="text-sm text-blue-800">
              {formData.type === "ai_calls"
                ? "This limit controls how much agents can spend on AI API calls"
                : "This limit controls how much agents can bill to users"}{" "}
              per {formData.period.replace("ly", "")}.
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-blue-500 hover:bg-blue-400 text-white"
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Adding...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Add Limit
                </span>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddSpendingLimitModal;
