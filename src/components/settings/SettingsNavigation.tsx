"use client";
import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "~/lib/utils";
import {
  DollarSign,
  Settings,
  Users,
  Shield,
  Bell,
  Database,
  Key,
  Globe,
} from "lucide-react";

const settingsNavItems = [
  {
    title: "General",
    href: "/dashboard/settings",
    icon: Settings,
    description: "Basic organization settings",
  },
  {
    title: "Spending Limits",
    href: "/dashboard/settings/spending-limits",
    icon: DollarSign,
    description: "Configure AI and billing spending limits",
  },
  {
    title: "User Management",
    href: "/dashboard/settings/users",
    icon: Users,
    description: "Manage team members and permissions",
  },
  {
    title: "Security",
    href: "/dashboard/settings/security",
    icon: Shield,
    description: "Security settings and access controls",
  },
  {
    title: "API Keys",
    href: "/dashboard/settings/api-keys",
    icon: Key,
    description: "Manage API keys and integrations",
  },
  {
    title: "Notifications",
    href: "/dashboard/settings/notifications",
    icon: Bell,
    description: "Configure alerts and notifications",
  },
  {
    title: "Data & Privacy",
    href: "/dashboard/settings/privacy",
    icon: Database,
    description: "Data handling and privacy settings",
  },
  {
    title: "Integrations",
    href: "/dashboard/settings/integrations",
    icon: Globe,
    description: "Third-party integrations and webhooks",
  },
];

export interface SettingsNavigationProps {
  className?: string;
}

const SettingsNavigation: React.FC<SettingsNavigationProps> = ({
  className = "",
}) => {
  const pathname = usePathname();

  return (
    <nav className={cn("w-full", className)}>
      <div className="space-y-1">
        {settingsNavItems.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 px-4 py-3 rounded-lg transition-colors",
                "hover:bg-gray-100 group",
                isActive
                  ? "bg-blue-50 text-blue-700 border-l-4 border-blue-500"
                  : "text-gray-700 hover:text-gray-900"
              )}
            >
              <Icon
                className={cn(
                  "w-5 h-5 transition-colors",
                  isActive
                    ? "text-blue-500"
                    : "text-gray-400 group-hover:text-gray-600"
                )}
              />
              <div className="flex-1">
                <div className="font-medium">{item.title}</div>
                <div className="text-sm text-gray-500 group-hover:text-gray-600">
                  {item.description}
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

export default SettingsNavigation;
