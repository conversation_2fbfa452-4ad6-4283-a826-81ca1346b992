"use client";
import { useContext, useEffect, useRef } from "react";
import { DataContext } from "~/store/GlobalState";
import axios from "axios";
import { Centrifuge } from "centrifuge";
import { ACTIONS } from "~/store/Actions";
// import { useParams } from "next/navigation";
import { ToastContainer } from "react-toastify";

/* eslint-disable */

//

export default function DMNotificationConnection() {
  // const params = useParams();
  // const id = params.id as string;
  const { dispatch } = useContext(DataContext);

  const audioPlayer = useRef<any>(null);


  const connectUrl: any = process.env.NEXT_PUBLIC_CONNECT_URL;

  // get connection token
  const getConnectionToken = async () => {
    const token = localStorage.getItem("token") || "";
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/connection`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data.data.token;
  };

  //fetch subscription token
  const getSubscriptionToken = async (channel: string) => {
    const token = localStorage.getItem("token") || "";

    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_BASE_URL}/token/subscription`,
      { channel },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.data.token;
  };

  // centrifugo connection for notification
  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "{}")
    // Initialize Centrifuge client
    const centrifugeClient: any = new Centrifuge(connectUrl, {
      getToken: getConnectionToken,
      debug: true,
    });

    centrifugeClient.on("connect", () => {
      console.log("Connected to Centrifuge");
    });

    centrifugeClient.on("disconnect", () => {
      console.log("Disconnected from Centrifuge");
    });

    // Function to get the token for the personal channel
    const getPersonalChannelSubscriptionToken = async () => {
      return getSubscriptionToken(user?.id);
    };

    // Create a subscription to the channel
    const sub = centrifugeClient.newSubscription(user?.id, {
      getToken: getPersonalChannelSubscriptionToken,
    });

    sub.on("subscribed", () => {
      // console.log("Subscription confirmeds for dm notification:", sub);
      dispatch({ type: ACTIONS.DM_NOTIFICATION_SUBSCRIPTION, payload: sub });
    });

    // message publishing
    sub.on("publication", (ctx: any) => {
      dispatch({ type: ACTIONS.NOTIFICATIONS, payload: ctx?.data });
      dispatch({ type: ACTIONS.NOTIFY, payload: ctx?.data?.data });
      console.log(ctx?.data)
      const result = ctx?.data


      // general notifications
      if (result?.section === "thread_message" && result?.notification_type == "new_message" && result?.data?.user_id !== user?.user_id) {
        playSound();
      }

      if (result?.section === "reply_message" && result?.notification_type === "new_message") {
        const message = ctx?.data?.data
        const updates = ctx?.data?.update_change

        dispatch({
          type: ACTIONS.UPDATE_DM_MESSAGE_THREAD,
          payload: {
            threadId: message.thread_id,
            reply: message,
            updates
          },
        });
      }

      // delete dm reply
      if (result?.section === "reply_message" && result?.notification_type === "deleted") {
        const message = ctx?.data?.modification_ids
        const updates = ctx?.data?.update_change

        dispatch({
          type: ACTIONS.DELETE_MESSAGE_THREAD_REPLY,
          payload: {
            threadId: message.thread_id,
            messageId: message?.message_id,
            updates
          },
        });

      }

      // delete dm message
      if (result?.section === "thread_message" && result?.notification_type === "deleted") {
        const message = ctx?.data?.modification_ids

        dispatch({
          type: ACTIONS.DELETE_DM_MESSAGE,
          payload: {
            threadId: message.thread_id,
          },
        });
      }

      // Edit dm message
      if (result?.section === "thread_message" && result?.notification_type === "updated") {
        const message = ctx?.data?.data

        dispatch({
          type: ACTIONS.EDIT_DM_MESSAGE,
          payload: {
            threadId: message.thread_id,
            newMessageData: message
          },
        });
      }

      // Edit reply message
      if (result?.section === "reply_message" && result?.notification_type === "updated") {
        const message = ctx?.data?.data
        const id = ctx?.data?.modification_ids?.message_id

        dispatch({
          type: ACTIONS.EDIT_REPLY_MESSAGE,
          payload: {
            threadId: id,
            newMessageData: message
          },
        });
      }

      // Thread count and mention count highlight
      if (result?.section === "channels_section" && result?.notification_type === "unread_thread_change") {
        // console.log(result)
        dispatch({ type: ACTIONS.THREAD_COUNT, payload: ctx.data.data.thread_count })
        dispatch({
          type: ACTIONS.UPDATE_THREAD_COUNT,
          payload: {
            channels_id: ctx.data.data.channels_id,
            mention_count: ctx.data.data.mention_count,
            thread_count: ctx.data.data.thread_count,
          },
        });
      }

      // DM notifications
      if (result?.section === "dm_channels_section" && result?.notification_type === "unread_thread_change") {
        dispatch({ type: ACTIONS.DM_COUNT, payload: ctx.data.data.thread_count })
        dispatch({
          type: ACTIONS.UPDATE_DM_COUNT,
          payload: {
            channel_id: ctx.data.data.channel_id,
            thread_count: ctx.data.data.thread_count,
          },
        });
      }

    });

    sub.on("error", (ctx: any) => {
      console.error(`Subscription error: ${ctx.message}`);
    });

    // Connect to Centrifuge and subscribe
    centrifugeClient.connect();
    sub.subscribe();

    // Cleanup on component unmount
    return () => {
      sub.unsubscribe();
      centrifugeClient.disconnect();
    };
  }, [connectUrl, dispatch]);


  const playSound = () => {
    audioPlayer?.current?.play();
  };

  //

  return (
    <div>
      <ToastContainer limit={1} />
      <audio controls ref={audioPlayer} style={{ display: "none" }}>
        <source src="/audio/message.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
    </div>
  )
}
