"use client";
import { useContext, useEffect, useRef, useState } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { useRouter } from "next/navigation";
import { PlusIcon, XIcon } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import {
  ChannelPlusIcon,
  ChatBubbleIcon,
  DropdownIcon,
  PencilIcon,
} from "~/svgs";
import { ChannelCard } from "~/app/(client)/client/_components/ChannelCard";
import { ChannelCardOthers } from "~/app/(client)/client/_components/ChannelCard/others";
import { ChannelCardArchive } from "~/app/(client)/client/_components/ChannelCard/archive";
import { AgentCard } from "~/app/(client)/client/_components/agent-card";
import { cn } from "~/lib/utils";
import CreateChannelDialog from "~/app/(client)/client/_components/chat-nav/create-channel-dialog";
import { PeopleHomeCard } from "~/app/(client)/client/_components/people-card";
import UseHomeChannel from "~/app/(client)/client/home/<USER>/hooks/home-channels";
import Link from "next/link";
import InviteModal from "~/app/(client)/client/_components/invite-modal";
import ChannelInviteModal from "~/app/(client)/client/_components/invite-modal/channel";
import OrganisationMenu from "~/app/(client)/client/_components/org-dropdown";

//

export default function ChannelNav() {
  const { state, dispatch } = useContext(DataContext);
  const router = useRouter();

  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Restore scroll position after re-render
  useEffect(() => {
    const savedScroll = sessionStorage.getItem("sidebar-scroll");
    if (sidebarRef.current && savedScroll) {
      sidebarRef.current.scrollTop = parseInt(savedScroll, 10);
    }
  }, []);

  // Save scroll position before re-render
  const handleScroll = () => {
    if (sidebarRef.current) {
      sessionStorage.setItem(
        "sidebar-scroll",
        sidebarRef.current.scrollTop.toString()
      );
    }
  };

  const [openAccordions, setOpenAccordions] = useState<string[]>(() => {
    if (typeof window !== "undefined") {
      return JSON.parse(
        localStorage.getItem("openAccordions") ||
          '["channels", "other channels", "archived channels"]'
      );
    }
    return ["channels", "other channels", "archived channels"];
  });

  // Function to toggle accordions independently
  const handleAccordionChanges = (values: string[]) => {
    setOpenAccordions(values);
    if (typeof window !== "undefined") {
      localStorage.setItem("openAccordions", JSON.stringify(values));
    }
  };

  const handleNewChat = () => {
    dispatch({ type: ACTIONS.CLEAR_CHATS });
    router.push("/client/home/<USER>");
  };

  //

  return (
    <>
      <UseHomeChannel />
      <div
        className={`fixed top-[60px] left-[145px] h-[calc(100vh-60px)] bg-blue-300 md:translate-x-0 ${state?.channelBar === true ? "translate-x-0" : "-translate-x-full "}
      h-[93vh] pt-6 flex flex-col gap-6 min-w-[300px] transition-transform duration-300 ease-in-out z-20 md:z-0`}
      >
        <div className="flex items-center justify-between px-4 ">
          <div className="flex items-center gap-[5px] md:justify-between w-full">
            <OrganisationMenu />

            <button type="button" onClick={handleNewChat}>
              <PencilIcon />
            </button>
          </div>

          <XIcon
            className="block md:hidden text-gray-500 cursor-pointer"
            onClick={() =>
              dispatch({ type: ACTIONS.CHANNEL_BAR, payload: false })
            }
          />
        </div>

        <div
          className="overflow-auto [&::-webkit-scrollbar]:hidden text-blue-50 cursor-pointer"
          ref={sidebarRef}
          onScroll={handleScroll}
        >
          <div className="flex-1 flex items-center gap-[2px] py-1 px-2 mt-3 mx-2 hover:bg-blue-200 hover:text-white rounded-lg ">
            <ChatBubbleIcon />
            <p
              className={cn(
                "text-[15px] leading-4 truncate w-[180px] text-blue-50"
              )}
            >
              Threads
            </p>
          </div>

          <div className="">
            <div>
              <Accordion
                type="multiple"
                className="w-full"
                value={openAccordions}
                onValueChange={handleAccordionChanges}
              >
                <AccordionItem value="channels" className="border-none">
                  <AccordionTrigger className="font-normal w-full py-0">
                    <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                      <DropdownIcon
                        className={`transition-transform duration-300 ${
                          openAccordions.includes("channels")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px]  font-medium">Channels</h3>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent>
                    <ul className="flex flex-col gap-1">
                      {state?.channels?.map((item: any, index: number) => (
                        <ChannelCard {...item} key={index} />
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <CreateChannelDialog>
                <li
                  className={cn(
                    "relative px-2 mx-2 py-[7px] flex items-center rounded-lg group hover:bg-blue-200 cursor-pointer"
                  )}
                >
                  <div className="flex-1 flex items-center gap-1">
                    <ChannelPlusIcon />

                    <p
                      className={cn(
                        "text-[15px] leading-4 capitalize truncate text-blue-50"
                      )}
                    >
                      Add Channels
                    </p>
                  </div>
                </li>
              </CreateChannelDialog>
            </div>

            {/* Agents */}
            {state?.agentDm?.length !== 0 && (
              <>
                <Accordion
                  type="multiple"
                  className="w-full"
                  value={openAccordions}
                  onValueChange={handleAccordionChanges}
                >
                  <AccordionItem value="agents" className="border-none">
                    <AccordionTrigger className="font-normal w-full py-0">
                      <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                        <DropdownIcon
                          className={`w-5 h-5 transition-transform duration-300 ${
                            openAccordions.includes("agents")
                              ? "rotate-0"
                              : "-rotate-90"
                          }`}
                        />
                        <h3 className="text-[15px]  font-medium">Agents</h3>
                      </div>
                    </AccordionTrigger>

                    <AccordionContent>
                      <ul className="flex flex-col gap-1">
                        {state?.agentDm?.map((item: any, index: number) => (
                          <AgentCard {...item} key={index} />
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                <Link
                  href="/client/agents/browse-agents"
                  className="flex items-center gap-[2px] py-[6px] px-2 mx-2 mt-0 hover:bg-blue-200 hover:text-white rounded-lg "
                >
                  <ChatBubbleIcon />
                  <p
                    className={cn(
                      "text-[15px] leading-4 truncate w-[180px] text-blue-50"
                    )}
                  >
                    Browse Agents
                  </p>
                </Link>
              </>
            )}

            {/* Other channels */}
            {state?.otherChannels?.length !== 0 && state?.otherChannels && (
              <Accordion
                type="multiple"
                // collapsible
                className="w-full"
                value={openAccordions}
                onValueChange={handleAccordionChanges}
              >
                <AccordionItem value="other channels" className="border-none">
                  <AccordionTrigger className="font-normal w-full py-0">
                    <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                      <DropdownIcon
                        className={`w-5 h-5 transition-transform duration-300 ${
                          openAccordions.includes("other channels")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px] font-medium">
                        Other Channels
                      </h3>
                    </div>
                  </AccordionTrigger>

                  <AccordionContent>
                    <ul className="flex flex-col gap-1">
                      {state?.otherChannels?.map((item: any, index: number) => {
                        return <ChannelCardOthers {...item} key={index} />;
                      })}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}

            {/* Archived channels */}
            {state?.archivedChannels?.length !== 0 &&
              state?.archivedChannels && (
                <Accordion
                  type="multiple"
                  // collapsible
                  className="w-full"
                  value={openAccordions}
                  onValueChange={handleAccordionChanges}
                >
                  <AccordionItem
                    value="archived channels"
                    className="border-none"
                  >
                    <AccordionTrigger className="font-normal w-full py-0">
                      <div className="relative py-3 mx-4 flex items-center gap-1 rounded-lg cursor-pointer w-full">
                        <DropdownIcon
                          className={`w-5 h-5 transition-transform duration-300 ${
                            openAccordions.includes("archived channels")
                              ? "rotate-0"
                              : "-rotate-90"
                          }`}
                        />
                        <h3 className="text-[15px] font-medium">
                          Archived Channels
                        </h3>
                      </div>
                    </AccordionTrigger>

                    <AccordionContent>
                      <ul className="flex flex-col gap-1">
                        {state?.archivedChannels?.map(
                          (item: any, index: number) => {
                            return <ChannelCardArchive {...item} key={index} />;
                          }
                        )}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              )}
          </div>

          {!state?.channelloading && (
            <Accordion
              type="multiple"
              className="w-full"
              value={openAccordions}
              onValueChange={handleAccordionChanges}
            >
              <AccordionItem value="people" className="border-none">
                <div className="relative py-3 mx-4 flex items-center justify-between rounded-lg cursor-pointer z-50 group">
                  <AccordionTrigger className="font-normal py-0">
                    <div className="relative flex items-center gap-1 rounded-lg cursor-pointer">
                      <DropdownIcon
                        className={`w-5 h-5 transition-transform duration-300 ${
                          openAccordions.includes("people")
                            ? "rotate-0"
                            : "-rotate-90"
                        }`}
                      />
                      <h3 className="text-[15px] leading-4 font-medium capitalize">
                        People
                      </h3>
                    </div>
                  </AccordionTrigger>

                  <div
                    className="flex items-center justify-center h-6 w-6 bg-blue-500 rounded gap-1 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={handleNewChat}
                  >
                    <PlusIcon className="size-4" />
                  </div>
                </div>

                <AccordionContent>
                  {state?.dms?.map((item: any, index: number) => (
                    <div className="mb-1" key={index}>
                      <PeopleHomeCard {...item} />
                    </div>
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          )}

          <li
            onClick={() =>
              dispatch({ type: ACTIONS.INVITE_MODAL, payload: true })
            }
            className={cn(
              "relative px-2 mx-2 py-[8px] mb-10 flex items-center rounded-lg group hover:bg-blue-200 cursor-pointer"
            )}
          >
            <div className="flex-1 flex items-center gap-1">
              <ChannelPlusIcon />

              <p className="text-[15px] leading-4 capitalize truncate text-blue-50/70">
                Invite People
              </p>
            </div>
          </li>
        </div>
      </div>

      {state?.inviteModal && <InviteModal />}
      {state?.channelInvite && <ChannelInviteModal />}
    </>
  );
}
