"use client";
import React from "react";
import MultiStepForm, {
  MultiStepFormProps,
} from "~/components/ui/MultiStepForm";
import { Step } from "~/components/ui/StepIndicator";

export interface OrganizationCreationStepperProps
  extends Omit<MultiStepFormProps, "steps" | "title"> {
  variant?: "default" | "welcome" | "dashboard";
  customSteps?: Step[];
}

const OrganizationCreationStepper: React.FC<
  OrganizationCreationStepperProps
> = ({
  variant = "default",
  customSteps,
  stepIndicatorProps = {},
  ...props
}) => {
  const defaultSteps: Step[] = [
    {
      id: 1,
      label: "Organization Details",
      description: "Basic information about your organization",
    },
    {
      id: 2,
      label: "Billing Plan",
      description: "Choose a plan that fits your needs",
    },
  ];

  const steps = customSteps || defaultSteps;

  const getVariantProps = () => {
    switch (variant) {
      case "welcome":
        return {
          title: "Create Your Organization",
          stepIndicatorProps: {
            ...stepIndicatorProps,
            className: `flex items-center gap-4 mb-6 justify-center ${stepIndicatorProps.className || ""}`,
            lineClassName: `flex-1 h-px bg-gray-200 max-w-20 ${stepIndicatorProps.lineClassName || ""}`,
          },
        };
      case "dashboard":
        return {
          title: "Create Organization",
          stepIndicatorProps: {
            ...stepIndicatorProps,
            className: `flex items-center gap-4 mb-6 ${stepIndicatorProps.className || ""}`,
          },
        };
      default:
        return {
          title: "Create Organization",
          stepIndicatorProps: {
            ...stepIndicatorProps,
            className: `flex items-center gap-4 mb-6 ${stepIndicatorProps.className || ""}`,
          },
        };
    }
  };

  const variantProps = getVariantProps();

  return (
    <MultiStepForm
      steps={steps}
      title={variantProps.title}
      stepIndicatorProps={{
        showLabels: true,
        size: "md",
        ...variantProps.stepIndicatorProps,
      }}
      nextButtonText="Next: Choose Billing Plan"
      submitButtonText="Create Organization"
      {...props}
    />
  );
};

export default OrganizationCreationStepper;
