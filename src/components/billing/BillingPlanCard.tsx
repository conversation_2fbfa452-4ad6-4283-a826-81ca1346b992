"use client";
import React from "react";
import { Check } from "lucide-react";
import { Button } from "~/components/ui/button";
import { BillingPlan } from "~/data/billingPlans";

export interface BillingPlanCardProps {
  plan: BillingPlan;
  isSelected?: boolean;
  isCurrentPlan?: boolean;
  showButton?: boolean;
  buttonText?: string;
  // eslint-disable-next-line no-unused-vars
  onSelect?: (plan: BillingPlan) => void;
  // eslint-disable-next-line no-unused-vars
  onAction?: (plan: BillingPlan) => void;
  variant?: "selection" | "subscription" | "display";
  className?: string;
  maxFeatures?: number;
}

const BillingPlanCard: React.FC<BillingPlanCardProps> = ({
  plan,
  isSelected = false,
  isCurrentPlan = false,
  showButton = false,
  buttonText,
  onSelect,
  onAction,
  variant = "selection",
  className = "",
  maxFeatures = 4,
}) => {
  const handleClick = () => {
    if (onSelect && variant === "selection") {
      onSelect(plan);
    }
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onAction) {
      onAction(plan);
    }
  };

  const getCardStyles = () => {
    const baseStyles =
      "relative p-6 rounded-lg border-2 transition-all duration-200";

    if (variant === "selection") {
      return `${baseStyles} cursor-pointer hover:shadow-md ${
        isSelected
          ? "border-blue-500 bg-blue-100"
          : "border-gray-200 hover:border-gray-300"
      }`;
    }

    if (variant === "subscription") {
      return `${baseStyles} hover:shadow-lg ${
        isCurrentPlan
          ? "border-blue-500 bg-blue-50"
          : "border-gray-200 hover:border-gray-300"
      }`;
    }

    return `${baseStyles} border-gray-200`;
  };

  const getButtonText = () => {
    if (buttonText) return buttonText;

    if (variant === "subscription") {
      if (isCurrentPlan) return "Current Plan";
      if (plan.isCustom) return "Contact Sales";
      return `Upgrade to ${plan.name}`;
    }

    return plan.isCustom ? "Contact Sales" : "Select Plan";
  };

  const isButtonDisabled = () => {
    return variant === "subscription" && isCurrentPlan;
  };

  return (
    <div className={`${getCardStyles()} ${className}`} onClick={handleClick}>
      {variant === "selection" && isSelected && (
        <div className="absolute top-4 right-4">
          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
            <Check className="w-4 h-4 text-white" />
          </div>
        </div>
      )}

      {/* Current plan badge */}
      {variant === "subscription" && isCurrentPlan && (
        <div className="absolute top-4 right-4">
          <span className="bg-blue-500 text-white text-xs font-semibold px-2 py-1 rounded-full">
            Current
          </span>
        </div>
      )}

      {/* Plan header */}
      <div className="mb-4">
        <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
        <div className="flex items-baseline mt-2">
          <span className="text-2xl font-bold text-gray-900">
            {plan.isCustom ? "Custom" : `$${plan.price}`}
          </span>
          {!plan.isCustom && (
            <span className="text-gray-500 ml-1">/{plan.period}</span>
          )}
        </div>
        <p className="text-sm text-gray-600 mt-2">{plan.description}</p>
      </div>

      {/* Features list */}
      <div className="space-y-2 mb-6">
        {plan.features.slice(0, maxFeatures).map((feature, index) => (
          <div key={index} className="flex items-start gap-2">
            <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span className="text-sm text-gray-700">{feature}</span>
          </div>
        ))}
        {plan.features.length > maxFeatures && (
          <div className="text-sm text-gray-500 mt-2">
            +{plan.features.length - maxFeatures} more features
          </div>
        )}
      </div>

      {/* Custom pricing note */}
      {plan.isCustom && !showButton && (
        <div className="mt-4 text-sm text-blue-500">
          Contact sales for pricing
        </div>
      )}

      {/* Action button */}
      {showButton && (
        <Button
          className={`w-full bg-blue-500 hover:bg-blue-400 text-white font-medium ${
            isButtonDisabled() ? "opacity-50 cursor-not-allowed" : ""
          }`}
          onClick={handleButtonClick}
          disabled={isButtonDisabled()}
        >
          {getButtonText()}
        </Button>
      )}
    </div>
  );
};

export default BillingPlanCard;
