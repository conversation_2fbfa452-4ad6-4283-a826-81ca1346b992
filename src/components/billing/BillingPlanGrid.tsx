"use client";
import React from "react";
import { billingPlans, BillingPlan } from "~/data/billingPlans";
import BillingPlanCard from "./BillingPlanCard";

export interface BillingPlanGridProps {
  plans?: BillingPlan[];
  selectedPlan?: BillingPlan | null;
  currentPlan?: string | null;
  // eslint-disable-next-line no-unused-vars
  onPlanSelect?: (plan: BillingPlan) => void;
  // eslint-disable-next-line no-unused-vars
  onPlanAction?: (plan: BillingPlan) => void;
  variant?: "selection" | "subscription" | "display";
  showButtons?: boolean;
  className?: string;
  cardClassName?: string;
  maxFeatures?: number;
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  excludePlans?: string[];
}

const BillingPlanGrid: React.FC<BillingPlanGridProps> = ({
  plans = billingPlans,
  selectedPlan,
  currentPlan,
  onPlanSelect,
  onPlanAction,
  variant = "selection",
  showButtons = false,
  className = "",
  cardClassName = "",
  maxFeatures = 4,
  columns = {
    sm: 1,
    md: 2,
    lg: 3,
    xl: 4,
  },
  excludePlans = [],
}) => {
  // Filter out excluded plans
  const filteredPlans = plans.filter((plan) => !excludePlans.includes(plan.id));

  // Generate grid classes based on columns configuration
  const getGridClasses = () => {
    const classes = ["grid", "gap-4"];

    if (columns.sm) classes.push(`grid-cols-${columns.sm}`);
    if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);

    return classes.join(" ");
  };

  const handlePlanSelect = (plan: BillingPlan) => {
    if (onPlanSelect) {
      onPlanSelect(plan);
    }
  };

  const handlePlanAction = (plan: BillingPlan) => {
    if (onPlanAction) {
      onPlanAction(plan);
    }
  };

  return (
    <div className={`${getGridClasses()} ${className}`}>
      {filteredPlans.map((plan) => (
        <BillingPlanCard
          key={plan.id}
          plan={plan}
          isSelected={selectedPlan?.id === plan.id}
          isCurrentPlan={currentPlan === plan.name || currentPlan === plan.id}
          showButton={showButtons}
          onSelect={handlePlanSelect}
          onAction={handlePlanAction}
          variant={variant}
          className={cardClassName}
          maxFeatures={maxFeatures}
        />
      ))}
    </div>
  );
};

export default BillingPlanGrid;
