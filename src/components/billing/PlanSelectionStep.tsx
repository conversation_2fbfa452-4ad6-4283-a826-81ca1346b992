"use client";
import React from "react";
import { BillingPlan } from "~/data/billingPlans";
import BillingPlanSelector from "./BillingPlanSelector";

interface PlanSelectionStepProps {
  selectedPlan: BillingPlan | null;
  // eslint-disable-next-line no-unused-vars
  onPlanSelect: (plan: BillingPlan) => void;
}

const PlanSelectionStep: React.FC<PlanSelectionStepProps> = ({
  selectedPlan,
  onPlanSelect,
}) => {
  return (
    <BillingPlanSelector
      title="Choose Your Plan"
      subtitle="Select a billing plan for your organization. You can change this later."
      selectedPlan={selectedPlan}
      onPlanSelect={onPlanSelect}
      variant="selection"
      showSelectionSummary={true}
      columns={{
        sm: 1,
        md: 2,
        lg: 2,
        xl: 2,
      }}
    />
  );
};

export default PlanSelectionStep;
