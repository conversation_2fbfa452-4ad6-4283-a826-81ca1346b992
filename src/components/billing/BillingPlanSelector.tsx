"use client";
import React from "react";
import { BillingPlan } from "~/data/billingPlans";
import BillingPlanGrid, { BillingPlanGridProps } from "./BillingPlanGrid";

export interface BillingPlanSelectorProps extends Omit<BillingPlanGridProps, 'variant'> {
  title?: string;
  subtitle?: string;
  showSelectionSummary?: boolean;
  selectionSummaryTitle?: string;
  variant?: "selection" | "subscription";
}

const BillingPlanSelector: React.FC<BillingPlanSelectorProps> = ({
  title = "Choose Your Plan",
  subtitle = "Select a billing plan for your organization. You can change this later.",
  showSelectionSummary = true,
  selectionSummaryTitle = "Selected Plan",
  selectedPlan,
  variant = "selection",
  className = "",
  ...gridProps
}) => {
  return (
    <div className={`w-full ${className}`}>
      
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-2">{title}</h3>
        <p className="text-[#6E6E6F] text-sm">
          {subtitle}
        </p>
      </div>

      <BillingPlanGrid
        {...gridProps}
        selectedPlan={selectedPlan}
        variant={variant}
      />

      {showSelectionSummary && selectedPlan && variant === "selection" && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">
            {selectionSummaryTitle}: {selectedPlan.name}
          </h4>
          <p className="text-sm text-gray-600">
            {selectedPlan.isCustom
              ? "Our sales team will contact you to discuss pricing and setup."
              : `You'll be charged $${selectedPlan.price}/${selectedPlan.period} after your organization is created.`}
          </p>
        </div>
      )}
    </div>
  );
};

export default BillingPlanSelector;
