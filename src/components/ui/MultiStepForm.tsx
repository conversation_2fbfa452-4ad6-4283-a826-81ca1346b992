"use client";
import React, { ReactNode } from "react";
import StepIndicator, { Step, StepIndicatorProps } from "./StepIndicator";
import { Button } from "./button";

export interface MultiStepFormProps {
  steps: Step[];
  currentStep: string | number;
  children: ReactNode;
  title?: string;
  subtitle?: string;
  showStepIndicator?: boolean;
  stepIndicatorProps?: Partial<StepIndicatorProps>;
  showNavigation?: boolean;
  nextButtonText?: string;
  previousButtonText?: string;
  submitButtonText?: string;
  isLoading?: boolean;
  canGoNext?: boolean;
  canGoPrevious?: boolean;
  isLastStep?: boolean;
  onNext?: () => void;
  onPrevious?: () => void;
  onSubmit?: () => void;
  onStepClick?: (stepId: string | number) => void;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  navigationClassName?: string;
  nextButtonClassName?: string;
  previousButtonClassName?: string;
  submitButtonClassName?: string;
}

const MultiStepForm: React.FC<MultiStepFormProps> = ({
  steps,
  currentStep,
  children,
  title,
  subtitle,
  showStepIndicator = true,
  stepIndicatorProps = {},
  showNavigation = true,
  nextButtonText = "Next",
  previousButtonText = "Previous",
  submitButtonText = "Submit",
  isLoading = false,
  canGoNext = true,
  canGoPrevious = true,
  isLastStep = false,
  onNext,
  onPrevious,
  onSubmit,
  onStepClick,
  className = "",
  headerClassName = "",
  contentClassName = "",
  navigationClassName = "",
  nextButtonClassName = "",
  previousButtonClassName = "",
  submitButtonClassName = "",
}) => {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const isFirstStep = currentStepIndex === 0;

  const handleNext = () => {
    if (onNext && canGoNext) {
      onNext();
    }
  };

  const handlePrevious = () => {
    if (onPrevious && canGoPrevious) {
      onPrevious();
    }
  };

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
  };

  const getStepTitle = () => {
    if (title) return title;
    const currentStepData = steps.find(step => step.id === currentStep);
    return currentStepData?.label || "";
  };

  const getStepSubtitle = () => {
    if (subtitle) return subtitle;
    const currentStepData = steps.find(step => step.id === currentStep);
    return currentStepData?.description || "";
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Header */}
      <div className={`mb-8 ${headerClassName}`}>
        {(title || subtitle) && (
          <div className="mb-6">
            {title && (
              <h2 className="font-semibold text-xl md:text-2xl mb-2">
                {getStepTitle()}
              </h2>
            )}
            {subtitle && (
              <p className="text-[#6E6E6F] text-md">
                {getStepSubtitle()}
              </p>
            )}
          </div>
        )}

        {/* Step Indicator */}
        {showStepIndicator && (
          <StepIndicator
            steps={steps}
            currentStep={currentStep}
            onStepClick={onStepClick}
            allowClickableSteps={!!onStepClick}
            {...stepIndicatorProps}
          />
        )}
      </div>

      {/* Content */}
      <div className={`mb-8 ${contentClassName}`}>
        {children}
      </div>

      {/* Navigation */}
      {showNavigation && (
        <div className={`flex gap-4 ${navigationClassName}`}>
          {!isFirstStep && (
            <Button
              type="button"
              onClick={handlePrevious}
              disabled={!canGoPrevious || isLoading}
              className={`flex-1 bg-gray-200 text-gray-700 font-semibold py-6 px-10 hover:bg-gray-300 ${previousButtonClassName}`}
            >
              {previousButtonText}
            </Button>
          )}

          {isLastStep ? (
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={isLoading}
              className={`flex-1 bg-blue-500 hover:bg-blue-400 font-semibold py-6 px-10 text-white ${submitButtonClassName}`}
            >
              {isLoading ? (
                <span className="flex items-center gap-x-2">
                  <span className="animate-pulse">Loading...</span>
                </span>
              ) : (
                submitButtonText
              )}
            </Button>
          ) : (
            <Button
              type="button"
              onClick={handleNext}
              disabled={!canGoNext || isLoading}
              className={`flex-1 bg-blue-500 hover:bg-blue-400 font-semibold py-6 px-10 text-white ${nextButtonClassName}`}
            >
              {nextButtonText}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiStepForm;
