import { ACTIONS } from "./Actions";

const reducers = (state: any, action: any) => {
  const { type, payload } = action;
  switch (type) {
    case ACTIONS.USER:
      return {
        ...state,
        user: payload,
      };
    case ACTIONS.TOKEN:
      return {
        ...state,
        token: payload,
      };
    case ACTIONS.ORG_ID:
      return {
        ...state,
        orgId: payload,
      };
    case ACTIONS.CALLBACK:
      return {
        ...state,
        callback: payload,
      };
    case ACTIONS.APP_CALLBACK:
      return {
        ...state,
        appCallback: payload,
      };
    case ACTIONS.LOADING:
      return {
        ...state,
        loading: payload,
      };
    case ACTIONS.CHANNEL_LOADING:
      return {
        ...state,
        channelloading: payload,
      };
    case ACTIONS.NOTIFY:
      return {
        ...state,
        notify: payload,
      };
    case ACTIONS.ONLINE_STATUS:
      return {
        ...state,
        onlineStatus: payload,
      };
    case ACTIONS.CHANNELS:
      return {
        ...state,
        channels: payload,
      };
    case ACTIONS.THREAD:
      return {
        ...state,
        thread: payload,
      };
    case ACTIONS.WEBHOOK_STATUS:
      return {
        ...state,
        webhookStatus: payload,
      };
    case ACTIONS.CHANNEL_DETAILS:
      return {
        ...state,
        channelDetails: payload,
      };
    case ACTIONS.CHANNEL_MODAL:
      return {
        ...state,
        channelModal: payload,
      };
    case ACTIONS.MEMBERS:
      return {
        ...state,
        members: payload,
      };
    case ACTIONS.OTHER_CHANNELS:
      return {
        ...state,
        otherChannels: payload,
      };
    case ACTIONS.ARCHIVED_CHANNELS:
      return {
        ...state,
        archivedChannels: payload,
      };
    case ACTIONS.CHANNEL_BAR:
      return {
        ...state,
        channelBar: payload,
      };
    case ACTIONS.VISIBLE:
      return {
        ...state,
        visible: payload,
      };
    case ACTIONS.VISIBLES:
      return {
        ...state,
        visibles: payload,
      };
    case ACTIONS.SHOW_THREAD:
      return {
        ...state,
        showThread: payload,
      };
    case ACTIONS.OPEN_SIDEBAR:
      return {
        ...state,
        openSidebar: payload,
      };
    case ACTIONS.SETTINGS_BAR:
      return {
        ...state,
        settingsBar: payload,
      };
    case ACTIONS.SHOW:
      return {
        ...state,
        show: payload,
      };
    case ACTIONS.CREATE_CHANNEL:
      return {
        ...state,
        createChannel: payload,
      };
    case ACTIONS.CREATE_SECTION:
      return {
        ...state,
        createSection: payload,
      };
    case ACTIONS.ADD_MEMBER:
      return {
        ...state,
        addMember: payload,
      };
    case ACTIONS.ORG_MEMBERS:
      return {
        ...state,
        orgMembers: payload,
      };
    case ACTIONS.SHOW_RANGE:
      return {
        ...state,
        showRange: payload,
      };
    case ACTIONS.SUMMARY_COUNT:
      return {
        ...state,
        summaryCount: payload,
      };
    case ACTIONS.SECTION_ID:
      return {
        ...state,
        sectionId: payload,
      };
    case ACTIONS.SECTIONS:
      return {
        ...state,
        sections: payload,
      };
    case ACTIONS.DELETE_SECTION:
      return {
        ...state,
        deleteSection: payload,
      };
    case ACTIONS.UNGROUPED_CHANNELS:
      return {
        ...state,
        ungroupedChannels: payload,
      };
    case ACTIONS.DMS:
      return {
        ...state,
        dms: payload,
      };
    case ACTIONS.IS_OPENED:
      return {
        ...state,
        isOpened: payload,
      };
    case ACTIONS.NOTIFICATIONS:
      return {
        ...state,
        notifications: [...state.notifications, payload],
      };

    case ACTIONS.SUMMARY_LOADING:
      return {
        ...state,
        summaryLoading: payload,
      };
    case ACTIONS.MESSAGES:
      if (action.payload?.isRealTime) {
        return {
          ...state,
          messages: [action.payload.newMessage, ...(state.messages || [])],
        };
      }

      return {
        ...state,
        messages:
          action.payload.newPage === 1
            ? action.payload.newThreads || []
            : [...(state.messages || []), ...(action.payload.newThreads || [])],
      };

    case ACTIONS.CHATS:
      if (action.payload?.isRealTime) {
        return {
          ...state,
          chats: [action.payload.newMessage, ...(state.chats || [])],
        };
      }

      return {
        ...state,
        chats:
          action.payload.newPage === 1
            ? action.payload.newThreads || []
            : [...(state.chats || []), ...(action.payload.newThreads || [])],
      };

    case ACTIONS.REPLIES:
      if (action.payload?.isRealTime) {
        return {
          ...state,
          replies: [action.payload.newMessage, ...(state.replies || [])],
        };
      }

      return {
        ...state,
        replies:
          action.payload.newPage === 1
            ? action.payload.newThreads || []
            : [...(state.replies || []), ...(action.payload.newThreads || [])],
      };

    case ACTIONS.CLEAR_CHATS:
      return {
        ...state,
        chats: [],
      };

    case ACTIONS.CLEAR_REPLIES:
      return {
        ...state,
        replies: [],
      };

    case ACTIONS.MESSAGE_LOADING:
      return {
        ...state,
        messageLoading: payload,
      };

    case ACTIONS.INTEGRATIONS:
      return {
        ...state,
        integrations: payload,
      };

    case ACTIONS.INTEGRATIONS_LOADING:
      return {
        ...state,
        integrationsLoading: payload,
      };
    case ACTIONS.SUBSCRIPTION_PLAN:
      return {
        ...state,
        subscriptionPlan: payload,
      };
    case ACTIONS.NOTIFICATION_TYPE:
      return {
        ...state,
        notificationType: payload,
      };
    case ACTIONS.NOTIFICATION_CALLBACK:
      return {
        ...state,
        notificationCallback: payload,
      };

    // -----------------------------------
    case ACTIONS.ORG_DATA:
      return {
        ...state,
        orgData: payload,
      };
    case ACTIONS.CHANNEL_CALLBACK:
      return {
        ...state,
        channelCallback: payload,
      };
    case ACTIONS.CHANNEL_SUBSCRIPTION:
      return {
        ...state,
        channelSubscription: payload,
      };
    case ACTIONS.CHAT_SUBSCRIPTION:
      return {
        ...state,
        chatSubscription: payload,
      };
    case ACTIONS.REPLY_SUBSCRIPTION:
      return {
        ...state,
        replySubscription: payload,
      };
    case ACTIONS.TYPING_SUBSCRIPTION:
      return {
        ...state,
        typingSubscription: payload,
      };
    case ACTIONS.NOTIFICATION_SUBSCRIPTION:
      return {
        ...state,
        notificationSubscription: payload,
      };
    case ACTIONS.DM_NOTIFICATION_SUBSCRIPTION:
      return {
        ...state,
        dmNotificationSubscription: payload,
      };
    case ACTIONS.AGENT_DM:
      return {
        ...state,
        agentDm: payload,
      };
    case ACTIONS.CHANNEL_AGENTS:
      return {
        ...state,
        channelAgents: payload,
      };
    case ACTIONS.MENTIONS:
      return {
        ...state,
        mentions: [...state.mentions, ...payload],
      };
    case ACTIONS.CLEAR_MENTIONS:
      return {
        ...state,
        mentions: [],
      };
    case ACTIONS.INVITE_MODAL:
      return {
        ...state,
        inviteModal: payload,
      };
    case ACTIONS.SHOW_PROFILE:
      return {
        ...state,
        showProfile: payload,
      };
    case ACTIONS.REPLY:
      return {
        ...state,
        reply: payload,
      };
    case ACTIONS.RECENT_DM:
      return {
        ...state,
        recentDm: payload,
      };
    case ACTIONS.RECENT_PEOPLE:
      return {
        ...state,
        recentPeople: payload,
      };
    case ACTIONS.PROFILE:
      return {
        ...state,
        profile: payload,
      };
    case ACTIONS.SHOW_USER_PROFILE:
      return {
        ...state,
        showUserProfile: payload,
      };
    case ACTIONS.PROFILE_CALLBACK:
      return {
        ...state,
        profileCallback: payload,
      };
    case ACTIONS.GROUP_CALLBACK:
      return {
        ...state,
        groupCallback: payload,
      };
    case ACTIONS.REPLY_CALLBACK:
      return {
        ...state,
        replyCallback: payload,
      };
    case ACTIONS.USER_TYPING:
      return {
        ...state,
        userTyping: payload,
      };

    case ACTIONS.UPDATE_MESSAGE_THREAD: {
      const { threadId, reply, updates } = action.payload;

      const updatedMessages = state.messages.map((msg: any) => {
        if (msg.thread_id === threadId) {
          const existingReplies = msg.messages || [];

          const userExists = existingReplies.some(
            (r: any) => r.user_id === reply.user_id
          );

          return {
            ...msg,
            last_reply: reply?.created_at,
            messages: userExists
              ? existingReplies
              : [...existingReplies, reply],
            message_count: updates?.thread_count,
          };
        }
        return msg;
      });

      return {
        ...state,
        messages: updatedMessages,
      };
    }

    case ACTIONS.UPDATE_DM_MESSAGE_THREAD: {
      const { threadId, reply, updates } = action.payload;

      const updatedMessages = state.chats.map((msg: any) => {
        if (msg.thread_id === threadId) {
          const existingReplies = msg.messages || [];

          const userExists = existingReplies.some(
            (r: any) => r.user_id === reply.user_id
          );

          return {
            ...msg,
            last_reply: reply?.created_at,
            messages: userExists
              ? existingReplies
              : [...existingReplies, reply],
            message_count: updates?.thread_count,
          };
        }
        return msg;
      });

      return {
        ...state,
        chats: updatedMessages,
      };
    }

    case ACTIONS.DELETE_CHANNEL_MESSAGE: {
      const { threadId } = action.payload;

      return {
        ...state,
        messages: (state.messages || []).filter(
          (msg: any) => msg.thread_id !== threadId
        ),
      };
    }

    case ACTIONS.DELETE_DM_MESSAGE: {
      const { threadId } = action.payload;

      return {
        ...state,
        chats: (state.chats || []).filter(
          (msg: any) => msg.thread_id !== threadId
        ),
      };
    }

    case ACTIONS.DELETE_MESSAGE_THREAD_REPLY: {
      const { threadId, messageId, updates } = action.payload;

      const updatedMessages = state.messages.map((msg: any) => {
        if (msg.thread_id === threadId) {
          const existingReplies = msg.messages || [];

          const filteredReplies = existingReplies.filter(
            (r: any) => r.id !== messageId
          );

          return {
            ...msg,
            messages: updates?.preview_section
              ? filteredReplies
              : existingReplies,
            message_count: updates?.thread_count,
          };
        }
        return msg;
      });

      return {
        ...state,
        replies: (state.replies || []).filter(
          (msg: any) => msg.id !== messageId
        ),
        messages: updatedMessages,
      };
    }

    case ACTIONS.EDIT_CHANNEL_MESSAGE: {
      const { threadId, newMessageData } = action.payload;

      const updatedMessages = (state.messages || []).map((msg: any) => {
        if (msg.thread_id === threadId) {
          return {
            ...msg,
            message: newMessageData.message,
            edited: true,
          };
        }
        return msg;
      });

      return {
        ...state,
        messages: updatedMessages,
      };
    }

    case ACTIONS.EDIT_DM_MESSAGE: {
      const { threadId, newMessageData } = action.payload;

      const updatedMessages = (state.chats || []).map((msg: any) => {
        if (msg.thread_id === threadId) {
          return {
            ...msg,
            message: newMessageData.message,
            edited: true,
          };
        }
        return msg;
      });

      return {
        ...state,
        chats: updatedMessages,
      };
    }

    case ACTIONS.EDIT_REPLY_MESSAGE: {
      const { threadId, newMessageData } = action.payload;

      const updatedMessages = (state.replies || []).map((msg: any) => {
        if (msg.id === threadId) {
          return {
            ...msg,
            message: newMessageData.message,
            edited: true,
          };
        }
        return msg;
      });

      return {
        ...state,
        replies: updatedMessages,
      };
    }

    case ACTIONS.IS_EDIT:
      return {
        ...state,
        isEdit: payload,
      };
    case ACTIONS.IS_EDIT_REPLY:
      return {
        ...state,
        isEditReply: payload,
      };
    case ACTIONS.THREAD_REPLY:
      return {
        ...state,
        threadReply: payload,
      };
    case ACTIONS.ROLE:
      return {
        ...state,
        role: payload,
      };
    case ACTIONS.STATUS_CALLBACK:
      return {
        ...state,
        statusCallback: payload,
      };
    case ACTIONS.TOP_LABEL:
      return {
        ...state,
        topLabel: payload,
      };
    case ACTIONS.ACTIVE_AGENTS:
      return {
        ...state,
        activeAgents: payload,
      };
    case ACTIONS.INACTIVE_AGENTS:
      return {
        ...state,
        inactiveAgents: payload,
      };
    case ACTIONS.MARKETPLACE_AGENTS:
      return {
        ...state,
        marketPlaceAgents: payload,
      };

    case ACTIONS.UPDATE_THREAD_COUNT: {
      const updatedChannelId = action.payload.channels_id;

      return {
        ...state,
        channels: state.channels.map((channel: any) => {
          if (channel.channels_id === updatedChannelId) {
            return {
              ...channel,
              mention_count: action.payload.mention_count,
              thread_count: action.payload.thread_count,
            };
          }
          return channel;
        }),
      };
    }
    case ACTIONS.UPDATE_DM_COUNT: {
      const updatedChannelId = action.payload.channel_id;

      return {
        ...state,
        dms: state.dms.map((dm: any) => {
          if (dm.channel_id === updatedChannelId) {
            return {
              ...dm,
              thread_count: action.payload.thread_count,
            };
          }
          return dm;
        }),
      };
    }

    case ACTIONS.COUNT_CALLBACK:
      return {
        ...state,
        countCallback: payload,
      };
    case ACTIONS.THREAD_COUNT:
      return {
        ...state,
        threadCount: payload,
      };
    case ACTIONS.DM_COUNT:
      return {
        ...state,
        dmCount: payload,
      };
    case ACTIONS.SHOW_BADGE:
      return {
        ...state,
        showBadge: payload,
      };
    case ACTIONS.USER_DATA:
      return {
        ...state,
        userData: payload,
      };
    case ACTIONS.HOVER_PROFILE:
      return {
        ...state,
        hoverProfile: payload,
      };
    case ACTIONS.STATUS:
      return {
        ...state,
        status: payload,
      };
    case ACTIONS.NOTIFICATION_DETAIL:
      return {
        ...state,
        notificationDetail: payload,
      };
    case ACTIONS.CHANNEL_INVITE:
      return {
        ...state,
        channelInvite: payload,
      };
    case ACTIONS.AGENT_MODAL:
      return {
        ...state,
        agentModal: payload,
      };
    case ACTIONS.AGENT_STATE:
      return {
        ...state,
        agentState: payload,
      };
    case ACTIONS.AGENT_CALLBACK:
      return {
        ...state,
        agentCallback: payload,
      };
    default:
      return state;
  }
};

export default reducers;
