export interface BillingPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  isPopular?: boolean;
  isCustom?: boolean;
}

export const billingPlans: BillingPlan[] = [
  {
    id: "free",
    name: "Free",
    price: 0,
    period: "month",
    description: "Perfect for getting started with basic features",
    features: [
      "Up to 2 channels",
      "Up to 5 team members",
      "50 notifications per day",
      "Basic support",
      "7-day message history",
    ],
  },
  {
    id: "starter",
    name: "Starter",
    price: 15,
    period: "month",
    description: "Ideal for small teams and startups",
    features: [
      "Up to 10 channels",
      "Up to 25 team members",
      "500 notifications per day",
      "Priority support",
      "30-day message history",
      "Basic integrations",
      "File sharing (100MB)",
    ],
  },
  {
    id: "business",
    name: "Business",
    price: 25,
    period: "month",
    description: "Perfect for growing teams and businesses",
    features: [
      "Up to 50 channels",
      "Up to 100 team members",
      "Unlimited notifications",
      "24/7 priority support",
      "Unlimited message history",
      "Advanced integrations",
      "File sharing (1GB)",
      "Team analytics",
      "Custom branding",
    ],
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: 0,
    period: "month",
    description: "For large organizations with advanced needs",
    features: [
      "Unlimited channels",
      "Unlimited team members",
      "Unlimited notifications",
      "Dedicated support manager",
      "Unlimited message history",
      "Custom integrations",
      "Unlimited file sharing",
      "Advanced analytics",
      "White-label solution",
      "SSO & SAML",
      "Advanced security",
      "Custom SLA",
    ],
    isCustom: true,
  },
];

export const getPlanById = (id: string): BillingPlan | undefined => {
  return billingPlans.find((plan) => plan.id === id);
};

export const getDefaultPlan = (): BillingPlan => {
  return billingPlans[0]; 
};

export const getPlanByLegacyName = (name: string): BillingPlan | undefined => {
  const nameMap: Record<string, string> = {
    "Starter": "starter",
    "Business": "business",
    "Enterprise": "enterprise",
    "Free": "free",
  };

  const planId = nameMap[name] || name.toLowerCase();
  return getPlanById(planId);
};

export const priceHelper = (planName: string): number => {
  const plan = getPlanByLegacyName(planName);
  return plan?.price || 0;
};


