"use client";
import cogoToast from "cogo-toast";
import React, { useState, useEffect } from "react";
import { Input } from "~/components/ui/input";
import { PostRequest, PutRequest } from "~/utils/request";
import { Country } from "country-state-city";
import CustomSelect from "~/components/ui/custom-select";
import useFirstChannel from "../../home/<USER>/hooks/first-channel";
import PlanSelectionStep from "~/components/billing/PlanSelectionStep";
import { BillingPlan, getDefaultPlan } from "~/data/billingPlans";
import OrganizationCreationStepper from "~/components/forms/OrganizationCreationStepper";

// initial states
const initialState = {
  organisationName: "",
  organisationEmail: "",
  organisationType: "",
};

const CreateOrganization: React.FC = () => {
  const [buttonloading, setButtonloding] = useState(false);
  const [values, setValues] = useState(initialState);
  const [country, setCountry] = useState<any>(null);
  const [countries, setCountries] = useState<any>([]);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedPlan, setSelectedPlan] =
    useState<BillingPlan>(getDefaultPlan());
  const { firstChannel } = useFirstChannel();

  const handleChange = (event: any) => {
    const { name, value } = event.target;
    setValues({ ...values, [name]: value });
  };

  useEffect(() => {
    const allcountries = Country.getAllCountries();
    const response = allcountries?.map((item) => ({
      label: item.name,
      value: item.name,
    }));

    setCountries(response);
  }, []);

  const validateForm = () => {
    const { organisationName, organisationType } = values;
    if (!organisationName || !organisationType) {
      cogoToast.error("Please fill in all required fields.");
      return false;
    }

    if (!selectedPlan) {
      cogoToast.error("Please select a billing plan.");
      return false;
    }

    return true;
  };

  const handleNextStep = () => {
    const { organisationName, organisationType } = values;
    if (!organisationName || !organisationType) {
      cogoToast.error("Please fill in all required fields.");
      return;
    }
    setCurrentStep(2);
  };

  const handlePreviousStep = () => {
    setCurrentStep(1);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    if (event && typeof event.preventDefault === "function") {
      event.preventDefault();
    }

    if (!validateForm()) {
      return;
    }

    const token = localStorage.getItem("token") || "";

    setButtonloding(true);

    const payload = {
      name: values.organisationName,
      type: values.organisationType,
      country: country?.value,
      package_id: selectedPlan.id,
    };

    const res = await PostRequest(`/organisations`, payload, token);

    if (res?.status === 200 || res?.status === 201) {
      cogoToast.success(res?.data.message);

      // after creating an organisation, switch to it immediately
      const switchpayload = {
        current_org: res?.data?.data?.id,
      };

      const result = await PutRequest(
        "/users/switch-org",
        switchpayload,
        token
      );

      if (result?.status === 200 || result?.status === 201) {
        localStorage.setItem("token", result?.data?.data?.access_token);
        localStorage.setItem("orgId", result?.data?.data?.organisation?.id);
      }

      const channelId = await firstChannel(
        result?.data?.data?.organisation?.id
      );

      window.location.href = `/client/home/<USER>/${channelId}`;
    } else {
      setButtonloding(false);
    }
  };

  return (
    <div className="w-full">
      <div className="w-full max-w-4xl mx-auto my-10 ">
        <div className="mx-2 mt-5 md:mx-10">
          <OrganizationCreationStepper
            currentStep={currentStep}
            subtitle={
              currentStep === 1
                ? "Input the details of your organization below"
                : "Choose a billing plan for your organization"
            }
            isLastStep={currentStep === 2}
            canGoNext={
              !!(
                currentStep === 1 &&
                values.organisationName &&
                values.organisationType
              )
            }
            canGoPrevious={currentStep === 2}
            isLoading={buttonloading}
            onNext={handleNextStep}
            onPrevious={handlePreviousStep}
            onSubmit={() => handleSubmit({} as React.FormEvent)}
            variant="default"
          >
            {currentStep === 1 && (
              <div>
                <div className="form-box mb-3">
                  <label className="mb-2 block text-sm font-semibold">
                    Organization Name
                  </label>
                  <Input
                    placeholder="Enter your organization Name"
                    className="focus:border-blue-500 py-6"
                    type="text"
                    value={values.organisationName}
                    onChange={handleChange}
                    name="organisationName"
                    required
                  />
                </div>
                <div className="form-box mb-3">
                  <label className="mb-2 block text-sm font-semibold">
                    Organization Type
                  </label>
                  <Input
                    placeholder="What does your organization do"
                    className="focus:border-blue-500 py-6"
                    type="text"
                    value={values.organisationType}
                    onChange={handleChange}
                    name="organisationType"
                    required
                  />
                </div>

                <div className="flex flex-col sm:flex-row align-center justify-between gap-6">
                  <div className="form-box sm:mb-3 w-full">
                    <label className="mb-2 block text-sm font-semibold">
                      Country
                    </label>

                    <CustomSelect
                      options={countries}
                      placeholder="Select an option..."
                      onChange={setCountry}
                      defaultValue={country}
                      isDisabled={false}
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <PlanSelectionStep
                selectedPlan={selectedPlan}
                onPlanSelect={setSelectedPlan}
              />
            )}
          </OrganizationCreationStepper>
        </div>
      </div>
    </div>
  );
};

export default CreateOrganization;
