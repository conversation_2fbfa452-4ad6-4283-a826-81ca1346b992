"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import Loading from "~/components/ui/loading";
import { GetRequest, PostRequest, PutRequest } from "~/utils/request";
import { Country } from "country-state-city";
import CustomSelect from "~/components/ui/custom-select";
import useFirstChannel from "../../home/<USER>/hooks/first-channel";
import PlanSelectionStep from "~/components/billing/PlanSelectionStep";
import { BillingPlan, getDefaultPlan } from "~/data/billingPlans";

// initial states
const initialState = {
  organisationName: "",
  organisationType: "",
};

const CreateOrganization: React.FC = () => {
  const router = useRouter();
  const [buttonloading, setButtonloding] = useState(false);
  const [values, setValues] = useState(initialState);
  const [loading, setLoading] = useState(true);
  const [countries, setCountries] = useState<any>([]);
  const [country, setCountry] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedPlan, setSelectedPlan] =
    useState<BillingPlan>(getDefaultPlan());
  const { firstChannel } = useFirstChannel();

  // check if a user is onboarded
  useEffect(() => {
    (async () => {
      const token = localStorage.getItem("token") || "";

      const userstatus = await GetRequest("/auth/onboard-status", token);

      if (userstatus?.data?.data?.status) {
        router.push("/client");
      } else {
        setLoading(false);
      }

      setLoading(false);
    })();
  }, [router]);

  useEffect(() => {
    const allcountries = Country.getAllCountries();
    const response = allcountries?.map((item) => ({
      label: item.name,
      value: item.name,
    }));

    setCountries(response);
  }, []);

  const handleChange = (event: any) => {
    const { name, value } = event.target;
    setValues({ ...values, [name]: value });
  };

  const handleNextStep = () => {
    const { organisationName, organisationType } = values;
    if (!organisationName || !organisationType) {
      alert("Please fill in all required fields.");
      return;
    }
    setCurrentStep(2);
  };

  const handlePreviousStep = () => {
    setCurrentStep(1);
  };

  //
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const { organisationName, organisationType } = values;
    if (!organisationName || !organisationType) {
      alert("Please fill in all required fields.");
      return;
    }

    if (!selectedPlan) {
      alert("Please select a billing plan.");
      return;
    }

    const token = localStorage.getItem("token") || "";

    setButtonloding(true);

    const payload = {
      name: values.organisationName,
      type: values.organisationType,
      country: country?.value,
      billing_plan: selectedPlan.id,
    };

    const res = await PostRequest("/organisations", payload, token);

    if (res?.status === 200 || res?.status === 201) {
      // onboard the user
      await PutRequest("/auth/onboard-status", {}, token);

      localStorage.setItem("orgId", res?.data?.data?.id);
      const channelId = await firstChannel(res?.data?.data?.id);

      window.location.href = `/client/home/<USER>/${channelId}`;
    } else {
      setButtonloding(false);
    }
  };

  //

  return (
    <div className="w-full mt-0">
      <div className="w-full max-w-4xl mx-auto">
        <div className="mt-10 mb-10 mx-auto">
          <div className="flex gap-2 justify-center md:hidden mb-20">
            <Image src="/TelexIcon.svg" alt="Icon" width={40} height={40} />
            <h1 className="font-semibold text-2xl text-center flex justify-center items-center">
              Telex
            </h1>
          </div>
        </div>

        <div className="mx-3 md:mx-10">
          {loading ? (
            <div className="w-full flex justify-center mt-20">
              <Loading width="40" height="40" color="blue" />
            </div>
          ) : (
            <div>
              <h2 className="font-semibold sm:text-3xl text-2xl text-center">
                Create Your Organization
              </h2>
              <p className=" text-[#6E6E6F] text-md mt-4 mb-6 text-center">
                {currentStep === 1
                  ? "Input the details of your organization below"
                  : "Choose a billing plan for your organization"}
              </p>

              {/* Step indicator */}
              <div className="flex items-center gap-4 mb-6 justify-center">
                <div
                  className={`flex items-center gap-2 ${
                    currentStep === 1 ? "text-blue-500" : "text-gray-400"
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      currentStep === 1
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    1
                  </div>
                  <span className="text-sm font-medium">
                    Organization Details
                  </span>
                </div>
                <div className="flex-1 h-px bg-gray-200 max-w-20"></div>
                <div
                  className={`flex items-center gap-2 ${
                    currentStep === 2 ? "text-blue-500" : "text-gray-400"
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                      currentStep === 2
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    2
                  </div>
                  <span className="text-sm font-medium">Billing Plan</span>
                </div>
              </div>

              {currentStep === 1 && (
                <div>
                  <div className="form-box mb-3">
                    <label className="mb-2 block text-sm">
                      Organization Name
                    </label>
                    <Input
                      placeholder="Enter your organization Name"
                      className="focus:border-blue-500 py-6"
                      type="text"
                      value={values.organisationName}
                      onChange={handleChange}
                      name="organisationName"
                    />
                  </div>
                  <div className="form-box mb-3">
                    <label className="mb-2 block text-sm">
                      Organization Type
                    </label>
                    <Input
                      placeholder="What does your organization do"
                      className="focus:border-blue-500 py-6"
                      type="text"
                      value={values.organisationType}
                      onChange={handleChange}
                      name="organisationType"
                    />
                  </div>

                  <div className="flex flex-col sm:flex-row align-center justify-between gap-6">
                    <div className="form-box sm:mb-3 w-full">
                      <label className="mb-2 block text-sm">Country</label>

                      <CustomSelect
                        options={countries}
                        placeholder="Select an option..."
                        onChange={setCountry}
                        defaultValue={country}
                        isDisabled={false}
                      />
                    </div>
                  </div>

                  <Button
                    type="button"
                    onClick={handleNextStep}
                    className="w-full bg-blue-500 hover:bg-blue-400 font-semibold my-3 py-6 px-10 text-white"
                  >
                    Next: Choose Billing Plan
                  </Button>
                </div>
              )}

              {currentStep === 2 && (
                <div>
                  <PlanSelectionStep
                    selectedPlan={selectedPlan}
                    onPlanSelect={setSelectedPlan}
                  />

                  <div className="flex gap-4 mt-8">
                    <Button
                      type="button"
                      onClick={handlePreviousStep}
                      className="flex-1 bg-gray-200 text-gray-700 font-semibold py-6 px-10 hover:bg-gray-300"
                    >
                      Previous
                    </Button>
                    <Button
                      type="submit"
                      onClick={handleSubmit}
                      className="flex-1 bg-blue-500 hover:bg-blue-400 font-semibold py-6 px-10 text-white"
                    >
                      {buttonloading ? (
                        <span className="flex items-center gap-x-2">
                          <span className="animate-pulse">Loading...</span>{" "}
                          <Loading width="20" height="40" />
                        </span>
                      ) : (
                        "Create Organization"
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateOrganization;
