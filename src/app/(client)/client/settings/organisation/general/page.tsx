"use client";
import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { <PERSON><PERSON> } from "~/components/ui/button";
import SpendingLimitsCard, { SpendingLimit } from "~/components/settings/SpendingLimitsCard";
import AddSpendingLimitModal, { NewSpendingLimit } from "~/components/settings/AddSpendingLimitModal";
import cogoToast from "cogo-toast";
import {
  DollarSign,
  Users,
  Shield,
  Bell,
  TrendingUp,
  AlertTriangle,
  Settings,
  Building,
  Globe,
  Plus,
  AlertCircle
} from "lucide-react";

const OrganisationGeneralSettingsPage: React.FC = () => {
  const [limits, setLimits] = useState<SpendingLimit[]>([]);
  const [showAddLimit, setShowAddLimit] = useState(false);
  const [isAddingLimit, setIsAddingLimit] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Mock data for spending limits
  const mockLimits: SpendingLimit[] = [
    {
      id: "ai_daily",
      type: "ai_calls",
      limit: 100.0,
      period: "daily",
      currentSpent: 75.5,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "ai_monthly",
      type: "ai_calls",
      limit: 2500.0,
      period: "monthly",
      currentSpent: 1850.25,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "billing_daily",
      type: "user_billing",
      limit: 500.0,
      period: "daily",
      currentSpent: 125.75,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
  ];

  React.useEffect(() => {
    // Load spending limits
    setTimeout(() => {
      setLimits(mockLimits);
    }, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdateLimit = async (limitId: string, newLimit: number) => {
    try {
      setLimits((prev) =>
        prev.map((limit) =>
          limit.id === limitId
            ? { ...limit, limit: newLimit, lastUpdated: new Date().toISOString() }
            : limit
        )
      );
      cogoToast.success("Spending limit updated successfully");
    } catch (error) {
      console.error("Failed to update spending limit:", error);
      cogoToast.error("Failed to update spending limit");
    }
  };

  const handleToggleLimit = async (limitId: string, isActive: boolean) => {
    try {
      setLimits((prev) =>
        prev.map((limit) =>
          limit.id === limitId
            ? { ...limit, isActive, lastUpdated: new Date().toISOString() }
            : limit
        )
      );
      cogoToast.success(`Spending limit ${isActive ? "enabled" : "disabled"} successfully`);
    } catch (error) {
      console.error("Failed to toggle spending limit:", error);
      cogoToast.error("Failed to toggle spending limit");
    }
  };

  const handleSaveAll = async () => {
    setIsSaving(true);
    try {
      setTimeout(() => {
        cogoToast.success("All settings saved successfully");
        setIsSaving(false);
      }, 1500);
    } catch (error) {
      console.error("Failed to save settings:", error);
      cogoToast.error("Failed to save settings");
      setIsSaving(false);
    }
  };

  const handleAddLimit = async (newLimit: NewSpendingLimit) => {
    setIsAddingLimit(true);
    try {
      const mockNewLimit: SpendingLimit = {
        id: `${newLimit.type}_${newLimit.period}_${Date.now()}`,
        type: newLimit.type,
        limit: newLimit.limit,
        period: newLimit.period,
        currentSpent: 0,
        isActive: true,
        lastUpdated: new Date().toISOString(),
      };

      setLimits((prev) => [...prev, mockNewLimit]);
      setShowAddLimit(false);
      cogoToast.success("Spending limit added successfully");
    } catch (error) {
      console.error("Failed to add spending limit:", error);
      cogoToast.error("Failed to add spending limit");
    } finally {
      setIsAddingLimit(false);
    }
  };

  const quickSettings = [
    {
      title: "User Management",
      description: "Manage team members and permissions",
      icon: Users,
      color: "text-green-500",
      bgColor: "bg-green-50",
    },
    {
      title: "Security Settings",
      description: "Configure security and access controls",
      icon: Shield,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
    },
    {
      title: "Notifications",
      description: "Set up alerts and notifications",
      icon: Bell,
      color: "text-yellow-500",
      bgColor: "bg-yellow-50",
    },
    {
      title: "Integrations",
      description: "Third-party integrations and webhooks",
      icon: Globe,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
    },
  ];

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Organisation Settings</h1>
          <p className="text-gray-600 mt-1">
            Manage your organization settings, team, and advanced configurations
          </p>
        </div>
        <Button
          onClick={handleSaveAll}
          disabled={isSaving}
          className="bg-blue-500 hover:bg-blue-400 text-white"
        >
          {isSaving ? "Saving..." : "Save All Changes"}
        </Button>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="w-5 h-5 text-blue-500" />
            General Information
          </CardTitle>
          <CardDescription>
            Basic organization information and settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organization Name
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter organization name"
                defaultValue="Acme Corporation"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organization Type
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option>Technology</option>
                <option>Healthcare</option>
                <option>Finance</option>
                <option>Education</option>
                <option>Other</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Country
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option>United States</option>
                <option>United Kingdom</option>
                <option>Canada</option>
                <option>Australia</option>
                <option>Other</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Time Zone
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option>UTC-8 (Pacific Time)</option>
                <option>UTC-5 (Eastern Time)</option>
                <option>UTC+0 (GMT)</option>
                <option>UTC+1 (Central European Time)</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-blue-500" />
            Quick Settings
          </CardTitle>
          <CardDescription>
            Common settings and configurations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickSettings.map((setting) => {
              const Icon = setting.icon;
              return (
                <div
                  key={setting.title}
                  className={`p-4 rounded-lg border-2 border-transparent hover:border-gray-200 transition-all cursor-pointer group ${setting.bgColor}`}
                >
                  <div className="flex flex-col items-center text-center gap-3">
                    <div className="p-3 rounded-lg bg-white shadow-sm">
                      <Icon className={`w-6 h-6 ${setting.color}`} />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 group-hover:text-gray-700">
                        {setting.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {setting.description}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings - Spending Limits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-blue-500" />
            Advanced Settings - Spending Limits
          </CardTitle>
          <CardDescription>
            Configure global spending limits for AI calls and user billing
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                ${limits.reduce((sum, limit) => sum + limit.currentSpent, 0).toFixed(2)}
              </div>
              <div className="text-sm text-blue-800">Total Spent</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {limits.filter((limit) => limit.isActive).length}
              </div>
              <div className="text-sm text-green-800">Active Limits</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {limits.filter(
                  (limit) =>
                    limit.isActive &&
                    limit.limit > 0 &&
                    limit.currentSpent / limit.limit >= 0.75
                ).length}
              </div>
              <div className="text-sm text-yellow-800">At Risk</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                ${(
                  limits.reduce((sum, limit) => sum + limit.limit, 0) -
                  limits.reduce((sum, limit) => sum + limit.currentSpent, 0)
                ).toFixed(2)}
              </div>
              <div className="text-sm text-gray-800">Remaining</div>
            </div>
          </div>

          {/* Add Limit Button */}
          <div className="flex justify-end mb-4">
            <Button
              onClick={() => setShowAddLimit(true)}
              className="bg-blue-500 hover:bg-blue-400 text-white"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Spending Limit
            </Button>
          </div>

          {/* Spending Limits Component */}
          <SpendingLimitsCard
            limits={limits}
            onUpdateLimit={handleUpdateLimit}
            onToggleLimit={handleToggleLimit}
            onSave={handleSaveAll}
            isLoading={isSaving}
            className="border-0 shadow-none p-0"
          />
        </CardContent>
      </Card>

      {/* Add Limit Modal */}
      <AddSpendingLimitModal
        isOpen={showAddLimit}
        onClose={() => setShowAddLimit(false)}
        onAdd={handleAddLimit}
        isLoading={isAddingLimit}
      />
    </div>
  );
};

export default OrganisationGeneralSettingsPage;
