"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { SpendingLimit } from "~/components/settings/SpendingLimitsCard";
import AddSpendingLimitModal, {
  NewSpendingLimit,
} from "~/components/settings/AddSpendingLimitModal";
import cogoToast from "cogo-toast";
import {
  DollarSign,
  Plus,
  AlertCircle,
  TrendingUp,
  Settings,
} from "lucide-react";

const BillingPage: React.FC = () => {
  const [limits, setLimits] = useState<SpendingLimit[]>([]);
  const [showAddLimit, setShowAddLimit] = useState(false);
  const [isAddingLimit, setIsAddingLimit] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Mock data for spending limits
  const mockLimits: SpendingLimit[] = [
    {
      id: "ai_daily",
      type: "ai_calls",
      limit: 100.0,
      period: "daily",
      currentSpent: 75.5,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "ai_monthly",
      type: "ai_calls",
      limit: 2500.0,
      period: "monthly",
      currentSpent: 1850.25,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "billing_daily",
      type: "user_billing",
      limit: 500.0,
      period: "daily",
      currentSpent: 125.75,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
  ];

  React.useEffect(() => {
    // Load spending limits
    setTimeout(() => {
      setLimits(mockLimits);
    }, 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdateLimit = async (limitId: string, newLimit: number) => {
    try {
      setLimits((prev) =>
        prev.map((limit) =>
          limit.id === limitId
            ? {
                ...limit,
                limit: newLimit,
                lastUpdated: new Date().toISOString(),
              }
            : limit
        )
      );
      cogoToast.success("Spending limit updated successfully");
    } catch (error) {
      console.error("Failed to update spending limit:", error);
      cogoToast.error("Failed to update spending limit");
    }
  };

  const handleToggleLimit = async (limitId: string, isActive: boolean) => {
    try {
      setLimits((prev) =>
        prev.map((limit) =>
          limit.id === limitId
            ? { ...limit, isActive, lastUpdated: new Date().toISOString() }
            : limit
        )
      );
      cogoToast.success(
        `Spending limit ${isActive ? "enabled" : "disabled"} successfully`
      );
    } catch (error) {
      console.error("Failed to toggle spending limit:", error);
      cogoToast.error("Failed to toggle spending limit");
    }
  };

  const handleSaveAll = async () => {
    setIsSaving(true);
    try {
      setTimeout(() => {
        cogoToast.success("All settings saved successfully");
        setIsSaving(false);
      }, 1500);
    } catch (error) {
      console.error("Failed to save settings:", error);
      cogoToast.error("Failed to save settings");
      setIsSaving(false);
    }
  };

  const handleAddLimit = async (newLimit: NewSpendingLimit) => {
    setIsAddingLimit(true);
    try {
      const mockNewLimit: SpendingLimit = {
        id: `${newLimit.type}_${newLimit.period}_${Date.now()}`,
        type: newLimit.type,
        limit: newLimit.limit,
        period: newLimit.period,
        currentSpent: 0,
        isActive: true,
        lastUpdated: new Date().toISOString(),
      };

      setLimits((prev) => [...prev, mockNewLimit]);
      setShowAddLimit(false);
      cogoToast.success("Spending limit added successfully");
    } catch (error) {
      console.error("Failed to add spending limit:", error);
      cogoToast.error("Failed to add spending limit");
    } finally {
      setIsAddingLimit(false);
    }
  };

  return (
    <div className="w-full max-w-5xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Billing & Spending
          </h1>
          <p className="text-gray-600 mt-1">
            Manage billing settings and configure spending limits
          </p>
        </div>
        <Button
          onClick={handleSaveAll}
          disabled={isSaving}
          className="bg-blue-500 hover:bg-blue-400 text-white"
        >
          {isSaving ? "Saving..." : "Save Changes"}
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border rounded-lg p-4">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-500" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                $
                {limits
                  .reduce((sum, limit) => sum + limit.currentSpent, 0)
                  .toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Total Spent</div>
            </div>
          </div>
        </div>
        <div className="bg-white border rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-green-500" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {limits.filter((limit) => limit.isActive).length}
              </div>
              <div className="text-sm text-gray-600">Active Limits</div>
            </div>
          </div>
        </div>
        <div className="bg-white border rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-yellow-500" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {
                  limits.filter(
                    (limit) =>
                      limit.isActive &&
                      limit.limit > 0 &&
                      limit.currentSpent / limit.limit >= 0.75
                  ).length
                }
              </div>
              <div className="text-sm text-gray-600">At Risk</div>
            </div>
          </div>
        </div>
        <div className="bg-white border rounded-lg p-4">
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-blue-500 rounded"></div>
            <div>
              <div className="text-2xl font-bold text-gray-900">
                $
                {(
                  limits.reduce((sum, limit) => sum + limit.limit, 0) -
                  limits.reduce((sum, limit) => sum + limit.currentSpent, 0)
                ).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Remaining</div>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Settings - Spending Limits */}
      <div className="bg-white border rounded-lg">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="w-5 h-5 text-blue-500" />
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Advanced Settings - Spending Limits
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  Configure global spending limits for AI calls and user billing
                </p>
              </div>
            </div>
            <Button
              onClick={() => setShowAddLimit(true)}
              className="bg-blue-500 hover:bg-blue-400 text-white"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Limit
            </Button>
          </div>
        </div>

        <div className="p-6">
          {/* Spending Limits List */}
          <div className="space-y-4">
            {limits.map((limit) => {
              const usagePercentage =
                limit.limit > 0 ? (limit.currentSpent / limit.limit) * 100 : 0;
              const getUsageStatus = (percentage: number) => {
                if (percentage >= 90)
                  return {
                    color: "bg-red-500",
                    status: "Critical",
                    textColor: "text-red-800",
                  };
                if (percentage >= 75)
                  return {
                    color: "bg-yellow-500",
                    status: "Warning",
                    textColor: "text-yellow-800",
                  };
                return {
                  color: "bg-green-500",
                  status: "Normal",
                  textColor: "text-green-800",
                };
              };
              const usageStatus = getUsageStatus(usagePercentage);

              return (
                <div
                  key={limit.id}
                  className="border rounded-lg p-4 bg-gray-50"
                >
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-white rounded-lg shadow-sm">
                        {limit.type === "ai_calls" ? (
                          <div className="w-5 h-5 bg-blue-500 rounded"></div>
                        ) : (
                          <div className="w-5 h-5 bg-green-500 rounded"></div>
                        )}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">
                          {limit.type === "ai_calls"
                            ? "AI API Calls"
                            : "User Billing"}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {limit.type === "ai_calls"
                            ? "Maximum amount agents can spend on AI API calls"
                            : "Maximum amount agents can bill to users"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${
                          limit.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {limit.isActive ? "Active" : "Inactive"}
                      </span>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full bg-white border ${usageStatus.textColor}`}
                      >
                        {usageStatus.status}
                      </span>
                    </div>
                  </div>

                  {/* Usage Progress */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                      <span>
                        Current Usage: ${limit.currentSpent.toFixed(2)}
                      </span>
                      <span>
                        Limit: ${limit.limit.toFixed(2)} / {limit.period}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${usageStatus.color}`}
                        style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 mt-1">
                      <span>{usagePercentage.toFixed(1)}% used</span>
                      <span>
                        ${(limit.limit - limit.currentSpent).toFixed(2)}{" "}
                        remaining
                      </span>
                    </div>
                  </div>

                  {/* Controls */}
                  <div className="flex items-center gap-4 pt-3 border-t">
                    <div className="flex items-center gap-2 flex-1">
                      <label className="text-sm font-medium text-gray-700">
                        Limit ($):
                      </label>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        defaultValue={limit.limit}
                        className="w-32 focus:border-blue-500"
                        placeholder="0.00"
                        onBlur={(e) => {
                          const newValue = parseFloat(e.target.value) || 0;
                          if (newValue !== limit.limit) {
                            handleUpdateLimit(limit.id, newValue);
                          }
                        }}
                      />
                      <span className="text-sm text-gray-500">
                        / {limit.period}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          handleToggleLimit(limit.id, !limit.isActive)
                        }
                        className={
                          limit.isActive
                            ? "text-red-600 hover:text-red-700"
                            : "text-green-600 hover:text-green-700"
                        }
                      >
                        {limit.isActive ? "Disable" : "Enable"}
                      </Button>
                    </div>
                  </div>

                  {/* Warning for high usage */}
                  {usagePercentage >= 75 && limit.isActive && (
                    <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md mt-4">
                      <AlertCircle className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm text-yellow-800">
                        {usagePercentage >= 90
                          ? "Critical: Spending limit almost reached!"
                          : "Warning: Approaching spending limit"}
                      </span>
                    </div>
                  )}

                  {/* Last updated */}
                  <div className="text-xs text-gray-500 mt-2">
                    Last updated: {new Date(limit.lastUpdated).toLocaleString()}
                  </div>
                </div>
              );
            })}

            {limits.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <DollarSign className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">
                  No spending limits configured
                </p>
                <p className="text-sm">
                  Add your first spending limit to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add Limit Modal */}
      <AddSpendingLimitModal
        isOpen={showAddLimit}
        onClose={() => setShowAddLimit(false)}
        onAdd={handleAddLimit}
        isLoading={isAddingLimit}
      />
    </div>
  );
};

export default BillingPage;
