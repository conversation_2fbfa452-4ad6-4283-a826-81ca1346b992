"use client";
import { useState } from "react";
import { Edit2Icon } from "lucide-react";
import SettingsLabel from "../../components/settings-label";
import UpdateProfile from "./components/update-profile";
import UpdateEmail from "./components/update-email";
import UpdatePhoneNumber from "./components/update-phone-number";
import DeleteAccount from "./components/delete-account";

const AccountPage = () => {
  const [profileDialog, setProfileDialog] = useState(false);
  const [emailDialog, setEmailDialog] = useState(false);
  const [numberDialog, setNumberDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);

  return (
    <div className="">
      <SettingsLabel />
      <div className="p-4">
        <div className="mb-4">
          <h1 className="text-base font-medium">Your Account Information</h1>
          <p className="text-sm text-[#344054]">
            Manage your account data with ease.
          </p>
        </div>

        <div className="flex items-start gap-4">
          {/* first profile */}
          <div className="border rounded-xl p-4 flex-1 relative">
            <div className="border absolute top-4 right-4 p-2 rounded-md cursor-pointer">
              <Edit2Icon
                className="w-5 h-5 "
                onClick={() => setProfileDialog(true)}
              />
            </div>
            <div className="h-36 w-36 border rounded-xl bg-[#F2F4F7] flex items-center justify-center">
              <img
                src="/bankole.png"
                alt="Jeremiah Bankole's Picture"
                className="w-full h-full object-cover rounded-xl"
              />
            </div>
            <div className="flex flex-col gap-4 mt-4">
              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Name</h3>
                <div className="flex items-center gap-2">
                  <p>Jeremiah Bankole</p>
                  <div className="w-2 h-2 rounded-full bg-[#D9D9D9]"></div>
                  <span className="text-[#667085]">@Alex Pratt</span>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Title</h3>
                <p>Product Owner</p>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Time Zone</h3>
                <p>(UTC +01:00) West Central Africa</p>
              </div>
            </div>
          </div>

          {/* Email and number section */}
          <div className="border rounded-xl p-4 flex-1 flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Email Address</h3>
                <p><EMAIL></p>
              </div>
              <div className="border p-2 rounded-md cursor-pointer">
                <Edit2Icon
                  className="w-5 h-5"
                  onClick={() => setEmailDialog(true)}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Phone Number</h3>
                <p>+****************</p>
              </div>
              <div className="border p-2 rounded-md cursor-pointer">
                <Edit2Icon
                  className="w-5 h-5"
                  onClick={() => setNumberDialog(true)}
                />
              </div>
            </div>
          </div>
        </div>

        <button
          onClick={() => setDeleteDialog(true)}
          className="inline-block py-2 px-4 mt-6 border rounded-[4px] border-[#F81404] text-[#F81404] text-sm font-semibold hover:bg-[#F81404] hover:text-white transition-colors duration-200"
        >
          Delete My Account
        </button>
      </div>
      {profileDialog && (
        <div className="absolute left-0 right-0 top-0 bg-transparent z-20 flex items-center justify-center py-10 backdrop-blur-sm bg-red-500 ">
          <UpdateProfile setProfileDialog={setProfileDialog} />
        </div>
      )}
      {emailDialog && (
        <div className="absolute left-0 right-0 top-0 h-full bg-transparent z-20 flex items-center justify-center py-10 backdrop-blur-sm bg-red-500 ">
          <UpdateEmail setEmailDialog={setEmailDialog} />
        </div>
      )}
      {numberDialog && (
        <div className="absolute left-0 right-0 top-0 h-full bg-transparent z-20 flex items-center justify-center py-10 backdrop-blur-sm bg-red-500 ">
          <UpdatePhoneNumber setNumberDialog={setNumberDialog} />
        </div>
      )}
      {deleteDialog && (
        <div className="absolute left-0 right-0 top-0 h-full bg-transparent z-20 flex items-center justify-center py-10 backdrop-blur-sm bg-red-500 ">
          <DeleteAccount setDeleteDialog={setDeleteDialog} />
        </div>
      )}
    </div>
  );
};

export default AccountPage;
