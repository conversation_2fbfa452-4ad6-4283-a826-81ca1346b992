"use client";
import React, { FormEvent, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>Off, CircleCheck } from "lucide-react";

const DeleteAccount = ({ setDeleteDialog }: { setDeleteDialog: Function }) => {
  const [isContinue, setIsContinue] = useState(false);
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [agreed, setAgreed] = useState(false);

  const isValid = password.length > 0 && agreed;

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    console.log("Account deleted successfully");
    // Add API logic here
    // Show success message and Redirect to login page
  };

  return (
    <div className="max-w-lg mx-auto bg-white shadow-lg rounded-xl border border-purple-300 mt-10 relative">
      <X
        className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl font-bold cursor-pointer"
        onClick={() => setDeleteDialog(false)}
      />
      <h2 className="text-xl font-semibold border-b p-4">Delete Account</h2>

      {!isContinue ? (
        <div className="p-6 space-y-3">
          <h3 className="text-sm font-medium">
            Are you sure you want to delete your account with{" "}
            <span className="text-[#667085]">Timbu Tech Ltd</span>? You will
            lose:
          </h3>
          <div className="border-b pb-4 mb-4 space-y-2 text-sm">
            <p>
              <CircleCheck className="inline mr-2 text-green-500" size={18} />
              Access to this workspace.
            </p>
            <p>
              <CircleCheck className="inline mr-2 text-green-500" size={18} />
              All your chats history.
            </p>
          </div>

          <p className="text-xs text-gray-500">
            <strong>Note:</strong> This action cannot be undone.{" "}
          </p>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={() => setDeleteDialog(false)}
              className="px-4 py-2 rounded-md border border-gray-300 text-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={() => setIsContinue(true)}
              className={`px-4 py-2 rounded-md text-white bg-purple-600 hover:bg-purple-700`}
            >
              Continue
            </button>
          </div>
        </div>
      ) : (
        <form className="space-y-5 p-6" onSubmit={handleSubmit}>
          <div className="mb-4">
            <label
              className="block text-sm font-medium mb-1"
              htmlFor="password"
            >
              Password
            </label>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                className="w-full border rounded px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <button
                type="button"
                onClick={() => setShowPassword((prev) => !prev)}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          <div className="flex items-start mb-6">
            <input
              id="consent"
              type="checkbox"
              checked={agreed}
              onChange={() => setAgreed((prev) => !prev)}
              className="mt-1 mr-2"
            />
            <label htmlFor="consent" className="text-sm text-gray-700">
              I absolve Telex of any responsibility in this account deletion.
            </label>
          </div>

          <button
            type="submit"
            disabled={!isValid}
            className={`w-full px-4 py-2 rounded text-white font-medium ${
              isValid
                ? "bg-red-600 hover:bg-red-700"
                : "bg-red-300 cursor-not-allowed"
            }`}
          >
            Delete Account
          </button>
        </form>
      )}
    </div>
  );
};

export default DeleteAccount;
