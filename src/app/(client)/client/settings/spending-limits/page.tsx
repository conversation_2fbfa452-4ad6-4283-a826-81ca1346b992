"use client";
import React, { useState, useEffect } from "react";
import cogoToast from "cogo-toast";
import SpendingLimitsCard, {
  SpendingLimit,
} from "~/components/settings/SpendingLimitsCard";
import AddSpendingLimitModal, {
  NewSpendingLimit,
} from "~/components/settings/AddSpendingLimitModal";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Plus, Settings, AlertCircle, TrendingUp } from "lucide-react";

const SpendingLimitsPage: React.FC = () => {
  const [limits, setLimits] = useState<SpendingLimit[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showAddLimit, setShowAddLimit] = useState(false);
  const [isAddingLimit, setIsAddingLimit] = useState(false);

  // Mock data - replace with actual API calls
  const mockLimits: SpendingLimit[] = [
    {
      id: "ai_daily",
      type: "ai_calls",
      limit: 100.0,
      period: "daily",
      currentSpent: 75.5,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "ai_monthly",
      type: "ai_calls",
      limit: 2500.0,
      period: "monthly",
      currentSpent: 1850.25,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "billing_daily",
      type: "user_billing",
      limit: 500.0,
      period: "daily",
      currentSpent: 125.75,
      isActive: true,
      lastUpdated: new Date().toISOString(),
    },
    {
      id: "billing_monthly",
      type: "user_billing",
      limit: 10000.0,
      period: "monthly",
      currentSpent: 3250.0,
      isActive: false,
      lastUpdated: new Date().toISOString(),
    },
  ];

  useEffect(() => {
    loadSpendingLimits();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadSpendingLimits = async () => {
    setIsLoading(true);
    try {
      // Replace with actual API call
      // const token = localStorage.getItem("token") || "";
      // const response = await GetRequest("/admin/spending-limits", token);

      // Mock API response
      setTimeout(() => {
        setLimits(mockLimits);
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error("Failed to load spending limits:", error);
      cogoToast.error("Failed to load spending limits");
      setIsLoading(false);
    }
  };

  const handleUpdateLimit = async (limitId: string, newLimit: number) => {
    try {
      // const token = localStorage.getItem("token") || "";

      // Replace with actual API call
      // await PutRequest(`/admin/spending-limits/${limitId}`, { limit: newLimit }, token);

      setLimits((prev) =>
        prev.map((limit) =>
          limit.id === limitId
            ? {
                ...limit,
                limit: newLimit,
                lastUpdated: new Date().toISOString(),
              }
            : limit
        )
      );

      cogoToast.success("Spending limit updated successfully");
    } catch (error) {
      console.error("Failed to update spending limit:", error);
      cogoToast.error("Failed to update spending limit");
    }
  };

  const handleToggleLimit = async (limitId: string, isActive: boolean) => {
    try {
      // const token = localStorage.getItem("token") || "";

      // Replace with actual API call
      // await PutRequest(`/admin/spending-limits/${limitId}/toggle`, { isActive }, token);

      setLimits((prev) =>
        prev.map((limit) =>
          limit.id === limitId
            ? { ...limit, isActive, lastUpdated: new Date().toISOString() }
            : limit
        )
      );

      cogoToast.success(
        `Spending limit ${isActive ? "enabled" : "disabled"} successfully`
      );
    } catch (error) {
      console.error("Failed to toggle spending limit:", error);
      cogoToast.error("Failed to toggle spending limit");
    }
  };

  const handleSaveAll = async () => {
    setIsSaving(true);
    try {
      // const token = localStorage.getItem("token") || "";

      // Replace with actual API call
      // await PostRequest("/admin/spending-limits/bulk-update", { limits }, token);

      // Mock save
      setTimeout(() => {
        cogoToast.success("All spending limits saved successfully");
        setIsSaving(false);
      }, 1500);
    } catch (error) {
      console.error("Failed to save spending limits:", error);
      cogoToast.error("Failed to save spending limits");
      setIsSaving(false);
    }
  };

  const handleAddLimit = async (newLimit: NewSpendingLimit) => {
    setIsAddingLimit(true);
    try {
      // const token = localStorage.getItem("token") || "";

      // Replace with actual API call
      // const response = await PostRequest("/admin/spending-limits", newLimit, token);

      // Mock API response
      const mockNewLimit: SpendingLimit = {
        id: `${newLimit.type}_${newLimit.period}_${Date.now()}`,
        type: newLimit.type,
        limit: newLimit.limit,
        period: newLimit.period,
        currentSpent: 0,
        isActive: true,
        lastUpdated: new Date().toISOString(),
      };

      setLimits((prev) => [...prev, mockNewLimit]);
      setShowAddLimit(false);
      cogoToast.success("Spending limit added successfully");
    } catch (error) {
      console.error("Failed to add spending limit:", error);
      cogoToast.error("Failed to add spending limit");
    } finally {
      setIsAddingLimit(false);
    }
  };

  const getOverallStatus = () => {
    const activeLimits = limits.filter((limit) => limit.isActive);
    const criticalLimits = activeLimits.filter(
      (limit) => limit.limit > 0 && limit.currentSpent / limit.limit >= 0.9
    );
    const warningLimits = activeLimits.filter(
      (limit) =>
        limit.limit > 0 &&
        limit.currentSpent / limit.limit >= 0.75 &&
        limit.currentSpent / limit.limit < 0.9
    );

    if (criticalLimits.length > 0) {
      return {
        status: "Critical",
        color: "bg-red-100 text-red-800",
        count: criticalLimits.length,
      };
    }
    if (warningLimits.length > 0) {
      return {
        status: "Warning",
        color: "bg-yellow-100 text-yellow-800",
        count: warningLimits.length,
      };
    }
    return { status: "Normal", color: "bg-green-100 text-green-800", count: 0 };
  };

  const overallStatus = getOverallStatus();

  if (isLoading) {
    return (
      <div className="w-full max-w-6xl mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Spending Limits</h1>
          <p className="text-gray-600 mt-1">
            Configure and monitor global spending limits for AI calls and user
            billing
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge className={overallStatus.color}>
            {overallStatus.status}
            {overallStatus.count > 0 && ` (${overallStatus.count})`}
          </Badge>
          <Button
            onClick={() => setShowAddLimit(true)}
            className="bg-blue-500 hover:bg-blue-400 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Limit
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  $
                  {limits
                    .reduce((sum, limit) => sum + limit.currentSpent, 0)
                    .toFixed(2)}
                </div>
                <div className="text-sm text-gray-600">Total Spent Today</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {limits.filter((limit) => limit.isActive).length}
                </div>
                <div className="text-sm text-gray-600">Active Limits</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {
                    limits.filter(
                      (limit) =>
                        limit.isActive &&
                        limit.limit > 0 &&
                        limit.currentSpent / limit.limit >= 0.75
                    ).length
                  }
                </div>
                <div className="text-sm text-gray-600">Limits at Risk</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 bg-blue-500 rounded"></div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  $
                  {(
                    limits.reduce((sum, limit) => sum + limit.limit, 0) -
                    limits.reduce((sum, limit) => sum + limit.currentSpent, 0)
                  ).toFixed(2)}
                </div>
                <div className="text-sm text-gray-600">Remaining Budget</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Spending Limits Component */}
      <SpendingLimitsCard
        limits={limits}
        onUpdateLimit={handleUpdateLimit}
        onToggleLimit={handleToggleLimit}
        onSave={handleSaveAll}
        isLoading={isSaving}
      />

      {/* Add Limit Modal */}
      <AddSpendingLimitModal
        isOpen={showAddLimit}
        onClose={() => setShowAddLimit(false)}
        onAdd={handleAddLimit}
        isLoading={isAddingLimit}
      />
    </div>
  );
};

export default SpendingLimitsPage;
