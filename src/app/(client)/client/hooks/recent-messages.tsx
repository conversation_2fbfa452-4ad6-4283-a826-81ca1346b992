import { useContext, useEffect } from "react";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import { GetRequest } from "~/utils/new-request";

const RecentMessages = () => {
  const { state, dispatch } = useContext(DataContext);
  const orgId = localStorage.getItem("orgId") || "";

  useEffect(() => {
    if (orgId) {
      const fetchDmMessages = async () => {
        const res = await GetRequest(`/organisations/${orgId}/recent-dm`);

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.RECENT_DM, payload: res?.data?.data });
        }
      };
      fetchDmMessages();
    }
  }, [orgId, state?.dmCount]);

  useEffect(() => {
    if (orgId) {
      const fetchPeopleMessages = async () => {
        const res = await GetRequest(`/organisations/${orgId}/recent-dm`);

        if (res?.status === 200 || res?.status === 201) {
          dispatch({ type: ACTIONS.RECENT_PEOPLE, payload: res?.data?.data });
        }
      };
      fetchPeopleMessages();
    }
  }, [orgId, state?.dmCount]);

  return <></>;
};

export default RecentMessages;
