"use client";

import { Search, EllipsisVertical, PlusIcon } from "lucide-react";
import PageHeader from "../../agents/components/page-header";
import { useContext, useEffect, useState, useCallback, useRef } from "react";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";
import { GetRequest, PostRequest } from "~/utils/new-request";
import ActiveAgents from "../../_components/agents/active-agents";
import InActiveAgents from "../../_components/agents/inactive-agents";
import AgentsMarketplace from "../../_components/agents/agent-marketplace";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import Loading from "~/components/ui/loading";
import cogoToast from "cogo-toast";
import { useRouter } from "next/navigation";

const BrowseAgents = () => {
  const { state, dispatch } = useContext(DataContext);
  const [activeTopTab, setActiveTopTab] = useState(state?.topLabel);
  const [url, setUrl] = useState("");
  const [createLoading, setCreateLoading] = useState(false);
  const agentModal = state?.agentModal;
  const [callback, setCallback] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  const getIntegrations = useCallback(async () => {
    const orgId = localStorage.getItem("orgId") || "";

    const res = await GetRequest(`/organisations/${orgId}/agents`);

    const active = res?.data?.data?.filter(
      (item: any) => item?.is_active === true
    );
    const inactive = res?.data?.data?.filter(
      (item: any) => item?.is_active === false
    );

    if (res?.status === 200 || res?.status === 201) {
      dispatch({ type: ACTIONS.ACTIVE_AGENTS, payload: active });
      dispatch({ type: ACTIONS.INACTIVE_AGENTS, payload: inactive });
    }
    dispatch({ type: ACTIONS.INTEGRATIONS_LOADING, payload: false });
  }, [dispatch]);

  useEffect(() => {
    getIntegrations();
  }, [getIntegrations]);

  // Fetch Integrations
  useEffect(() => {
    const getIntegrations = async () => {
      const res = await GetRequest("/agents");
      if (res?.status === 200 || res?.status == 201) {
        dispatch({
          type: ACTIONS.MARKETPLACE_AGENTS,
          payload: res?.data?.data,
        });
      }
    };
    getIntegrations();
  }, [dispatch]);

  const handleRoute = (label: string) => {
    dispatch({ type: ACTIONS.TOP_LABEL, payload: label });
    setActiveTopTab(label);
  };

  // Handle popover click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setIsPopoverOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle save agent
  const handleSave = async (e: any) => {
    e.preventDefault();

    const orgId = localStorage.getItem("orgId") || "";
    setCreateLoading(true);

    const payload = {
      json_url: url,
    };

    const res = await PostRequest(`/organisations/${orgId}/agents`, payload);
    if (res?.status === 200 || res?.status === 201) {
      setCallback(!callback);
      cogoToast.success(res?.data?.message);
      setTimeout(() => {
        dispatch({ type: ACTIONS.AGENT_MODAL, payload: false });
        setUrl("");
        router.push(
          `/client/agents/browse-agents/${res?.data?.data?.id}?json_url=${encodeURIComponent(res?.data?.data?.json_url)}`
        );
      }, 1000);
    }
    setCreateLoading(false);
  };

  // Show modal
  const handleModal = () => {
    dispatch({ type: ACTIONS.AGENT_MODAL, payload: true });
    setIsPopoverOpen(false);
  };

  return (
    <div className="w-full">
      <PageHeader
        title="Browse Agents"
        buttonIcon={
          <Search
            size={20}
            className="text-gray-600 cursor-pointer hover:text-gray-800"
          />
        }
        moreIcon={
          <div className="relative" ref={popoverRef}>
            <EllipsisVertical
              className="w-5 h-5 text-gray-600 cursor-pointer hover:text-gray-800"
              onClick={() => setIsPopoverOpen(!isPopoverOpen)}
            />
            {isPopoverOpen && (
              <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-20">
                <div className="py-1">
                  <Button
                    onClick={handleModal}
                    className="flex items-center justify-start w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-none bg-transparent"
                  >
                    <PlusIcon className="w-5 h-5" color="#8686F9" />
                    <span className="ml-1 text-[13px] font-semibold text-blue-200">
                      Add an Agent
                    </span>
                  </Button>
                </div>
              </div>
            )}
          </div>
        }
      />

      <div className="py-3">
        {/* Top Tabs */}
        <div className={`flex items-center text-sm space-x-6 px-4 border-b`}>
          <span
            onClick={() => handleRoute("Active")}
            className={`cursor-pointer flex items-center pb-4 ${
              activeTopTab === "Active"
                ? "text-black font-semibold border-b-2 border-blue-300"
                : "text-gray-500"
            }`}
          >
            Active
            <span
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                activeTopTab === "Active"
                  ? "bg-purple-100 text-purple-600"
                  : "bg-gray-100"
              }`}
            >
              {state?.activeAgents?.length}
            </span>
          </span>

          <span
            onClick={() => handleRoute("Inactive")}
            className={`cursor-pointer flex items-center pb-4 ${
              activeTopTab === "Inactive"
                ? "text-black font-semibold border-b-2 border-blue-300"
                : "text-gray-500"
            }`}
          >
            Inactive
            <span
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                activeTopTab === "Inactive"
                  ? "bg-purple-100 text-purple-600"
                  : "bg-gray-100"
              }`}
            >
              {state?.inactiveAgents?.length}
            </span>
          </span>

          <span
            onClick={() => handleRoute("Agent Marketplace")}
            className={`cursor-pointer flex items-center pb-4 ${
              activeTopTab === "Agent Marketplace"
                ? "text-black font-semibold border-b-2 border-blue-300"
                : "text-gray-500"
            }`}
          >
            Agent Marketplace
            <span
              className={`ml-1 px-2 py-0.5 rounded-full text-xs ${
                activeTopTab === "Agent Marketplace"
                  ? "bg-purple-100 text-purple-600"
                  : "bg-gray-100"
              }`}
            >
              {state?.marketPlaceAgents?.length}
            </span>
          </span>
        </div>

        <div>
          {activeTopTab === "Active" && <ActiveAgents />}
          {activeTopTab === "Inactive" && <InActiveAgents />}
          {activeTopTab === "Agent Marketplace" && <AgentsMarketplace />}
        </div>
      </div>

      {/* Add agent modal */}
      <Dialog
        open={agentModal}
        onOpenChange={() =>
          dispatch({ type: ACTIONS.AGENT_MODAL, payload: !agentModal })
        }
      >
        <DialogContent className="w-full max-w-md">
          <DialogHeader className="mb-5">
            <DialogTitle className="text-blue-500">
              Provide your Agent Json url
            </DialogTitle>
          </DialogHeader>

          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Json url</label>
            <Input
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="Enter json url"
            />
          </div>

          <DialogFooter className="mt-4 flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() =>
                dispatch({ type: ACTIONS.AGENT_MODAL, payload: false })
              }
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={!url}
              className="bg-blue-500 gap-1 text-white px-8"
            >
              Save
              {createLoading && <Loading />}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BrowseAgents;
