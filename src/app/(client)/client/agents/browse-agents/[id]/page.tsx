"use client";

import { useContext, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  CheckCircle2,
  Search,
  ChevronRight,
  DollarSign,
  AlertCircle,
} from "lucide-react";
import Image from "next/image";
import PageHeader from "../../components/page-header";
import { DataContext } from "~/store/GlobalState";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import axios from "axios";
import images from "~/assets/images";
import { Switch } from "~/components/ui/switch";
import { GetRequest, PatchRequest } from "~/utils/new-request";
import Loading from "~/components/ui/loading";
import AgentSettings from "../../../_components/agents/settings";
import { ACTIONS } from "~/store/Actions";
import OutputSettings from "../../../_components/agents/output";
import AgentUsage from "../../../_components/agents/usage";

interface Field {
  label: string;
  description?: string;
  type:
    | "text"
    | "checkbox"
    | "number"
    | "dropdown"
    | "multi-checkbox"
    | "radio"
    | "multi-select";
  required: boolean;
  default?: string | number | boolean | string[];
  options?: string[];
}

export default function AgentProfile() {
  const [activeTab, setActiveTab] = useState("Details");
  const [copied, setCopied] = useState(false);
  const { state, dispatch } = useContext(DataContext);
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  const [agent, setAgent] = useState<any>([]);
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const json = searchParams.get("json_url") || "";
  const [isActive, setIsActive] = useState(false);
  const [multiSelectValues, setMultiSelectValues] = useState<
    Record<string, string[]>
  >({});
  const [settings, setSettings] = useState<Field[]>([]);
  const [data, setData] = useState<Record<string, any>>({});
  const [billingLimit, setBillingLimit] = useState<number>(100); // Default $100 limit
  const [isEditingLimit, setIsEditingLimit] = useState(false);
  const [tempLimit, setTempLimit] = useState<string>("100");

  // get single integration
  useEffect(() => {
    if (id) {
      const getIntegrations = async () => {
        try {
          const res = await axios.get(json, {
            headers: {
              "Content-Type": "application/json",
            },
          });

          setAgent(res.data);
          setLoading(false);
        } catch (err) {
          setLoading(false);
        }
      };
      getIntegrations();
      getStatus();
    }
  }, [json, id]);

  // get agent status
  const getStatus = async () => {
    const orgId = localStorage.getItem("orgId") || "";

    const res = await GetRequest(`/organisations/${orgId}/agents/${id}/status`);

    if (res?.status === 200 || res?.status === 201) {
      setIsActive(res?.data?.data?.is_active);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(agent?.descriptions?.app_url);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  // Handle billing limit functions
  const handleEditLimit = () => {
    setTempLimit(billingLimit.toString());
    setIsEditingLimit(true);
  };

  const handleSaveLimit = async () => {
    const newLimit = parseFloat(tempLimit);
    if (newLimit >= 0) {
      setBillingLimit(newLimit);
      setIsEditingLimit(false);

      // Here you would typically save to backend
      try {
        // const orgId = localStorage.getItem("orgId") || "";
        // const res = await PatchRequest(`/organisations/${orgId}/agents/${id}/billing-limit`, {
        //   billing_limit: newLimit
        // });
        console.log("Billing limit updated to:", newLimit);
      } catch (error) {
        console.error("Failed to update billing limit:", error);
      }
    }
  };

  const handleCancelEdit = () => {
    setTempLimit(billingLimit.toString());
    setIsEditingLimit(false);
  };

  // Function to handle switch change
  const handleSwitchChange = async (checked: boolean, id: string) => {
    const orgId = localStorage.getItem("orgId") || "";

    const payload = {
      integration_id: id,
      status: checked,
    };

    const res = await PatchRequest(
      `/organisations/${orgId}/agents/change_status`,
      payload
    );

    if (res?.status === 200 || res?.status === 201) {
      setIsActive(checked);
      setAgent((prev: any) => ({
        ...prev,
        is_active: checked,
      }));
      dispatch({ type: ACTIONS.CALLBACK, payload: !state?.callback });
    }
  };

  useEffect(() => {
    const orgId = localStorage.getItem("orgId") || "";

    const getSettings = async () => {
      const res = await GetRequest(
        `/organisations/${orgId}/integrations/custom/${id}/settings`
      );
      if (res?.status === 200 || res?.status === 201) {
        const integrationSettings = res?.data?.data.settings;

        if (
          Array.isArray(integrationSettings) &&
          integrationSettings.length > 0
        ) {
          setSettings(integrationSettings);

          // Populate the default values based on field type
          setData(
            integrationSettings.reduce(
              (acc: Record<string, any>, field: Field) => {
                const defaultValue =
                  field.type === "multi-checkbox" &&
                  Array.isArray(field.default)
                    ? field.default
                    : field.default || "";

                return { ...acc, [field.label]: defaultValue };
              },
              {}
            )
          );

          setMultiSelectValues(
            integrationSettings.reduce(
              (acc: Record<string, string[]>, field: Field) => {
                return {
                  ...acc,
                  [field.label]:
                    field.type === "multi-select" &&
                    Array.isArray(field.default)
                      ? field.default
                      : typeof field.default === "string"
                        ? field.default.split(",").map((v) => v.trim())
                        : [],
                };
              },
              {}
            )
          );
        }
      }
    };

    if (id) {
      getSettings();
    }
  }, [id]);

  const TABS = [
    "Details",
    ...(isActive ? ["Settings", "Output"] : []),
    "Usage",
  ];

  //

  return (
    <>
      <PageHeader
        title="Agent Profile"
        buttonIcon={
          <Search
            size={20}
            className="text-gray-600 cursor-pointer hover:text-gray-800"
          />
        }
      />

      {loading ? (
        <div className="flex items-center justify-center mt-20">
          <Loading width="40" height="40" color="blue" />
        </div>
      ) : (
        <>
          <div className="flex items-center gap-3 text-sm py-3 border-b px-5">
            <span
              onClick={() => router.back()}
              className="text-[#667085] cursor-pointer"
            >
              {state?.topLabel} Agents
            </span>
            <ChevronRight />
            <span>{agent?.descriptions?.app_name}</span>
          </div>

          <div className="bg-white p-6 max-w-6xl mx-auto">
            {/* Header */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="flex items-start gap-4">
                <div className="w-100 h-100 border rounded-xl">
                  <Image
                    src={agent?.descriptions?.app_logo || images?.bot}
                    alt={agent?.descriptions?.app_name}
                    width={100}
                    height={100}
                    className="rounded-xl bg-green-100"
                  />
                </div>

                <div>
                  <h2 className="text-lg font-semibold flex items-center gap-2">
                    {agent?.name}
                    <span className="text-xs px-3 border py-0.5 rounded-md text-gray-500">
                      2nd
                    </span>
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {agent?.description}
                  </p>
                  <div className="mt-2 flex items-center gap-2">
                    <a
                      href={agent?.provider?.url}
                      className="text-sm text-indigo-600 bg-indigo-50 px-2 py-1 rounded font-medium"
                    >
                      {agent?.provider?.url}
                    </a>
                    <button
                      onClick={handleCopy}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      {copied ? (
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>

              <div
                className={`flex items-center gap-2 rounded-3xl border ${agent?.is_active ? "border-[#7141F8]" : ""} py-5 px-3 h-0`}
              >
                <span
                  className={`text-sm ${agent?.is_active ? "text-[#7141F8]" : "text-[#667085]"}`}
                >
                  {agent?.is_active ? "Enabled" : "Disabled"}
                </span>

                <Switch
                  className="bg-green-500"
                  checked={isActive}
                  onCheckedChange={(checked) => {
                    handleSwitchChange(checked, id);
                  }}
                />
              </div>
            </div>

            {/* Tabs */}
            <div className="mt-8 border-b border-gray-200">
              <div className="flex gap-4">
                {TABS.map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-3 py-2 text-sm font-medium ${
                      activeTab === tab
                        ? "text-indigo-600 border-b-2 border-indigo-600"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === "Details" && (
              <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
                {/* Left */}
                <div className="lg:col-span-2">
                  <h3 className="font-semibold text-sm text-gray-800 mb-4">
                    Why Use {agent?.name}
                  </h3>
                  <ul className="text-sm text-gray-700 space-y-3 pl-4">
                    {agent?.skills?.map((item: any, index: number) => (
                      <li key={index}>
                        ✔ {item.name}
                        {item?.examples?.length > 0 && (
                          <ul className="list-disc pl-6 mt-2 space-y-1">
                            {item.examples.map((example: any, key: number) => (
                              <li key={key}>{example}</li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>

                  {/* Images */}
                  {/* <div className="mt-6 flex flex-wrap gap-4">
                                {Array.from({ length: 3 }).map((_, i) => (
                                    <div key={i} className="w-60 h-36 rounded-lg border overflow-hidden">
                                        <Image
                                            src={`/placeholders/image-${i + 1}.png`}
                                            alt={`preview-${i + 1}`}
                                            width={240}
                                            height={144}
                                            className="object-cover w-full h-full"
                                        />
                                    </div>
                                ))}
                            </div> */}
                </div>

                {/* Right */}
                <div>
                  <div className="bg-gray-50 border rounded-md p-4">
                    <p className="text-sm text-gray-700 text-center">
                      Every time Ruby helps you with managing your account, she
                      charges
                      <span className="block mt-1 text-green-600 font-semibold text-sm">
                        $0.01
                      </span>
                    </p>
                  </div>

                  <div className="flex items-center justify-center gap-2 mt-2 border rounded-md py-2">
                    <span className="block text-green-600 font-semibold text-sm">
                      $0.01
                    </span>
                    <span className="text-xs text-gray-500">
                      per task executed
                    </span>
                  </div>

                  {/* Billing Limit Section */}
                  <div className="mt-4 bg-gradient-to-r from-primary-500 to-primary-400 border-2 border-primary-300 rounded-lg p-4 shadow-lg">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="p-1 bg-white rounded-full">
                        <DollarSign className="w-5 h-5 text-primary-500" />
                      </div>
                      <h4 className="text-base font-bold text-white">
                        💰 Billing Limit
                      </h4>
                    </div>

                    <p className="text-sm text-primary-100 mb-4 font-medium">
                      Maximum amount this agent can bill per month
                    </p>

                    {!isEditingLimit ? (
                      <div className="bg-white rounded-lg p-3 shadow-md">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-2xl font-bold text-primary-500">
                              ${billingLimit.toFixed(2)}
                            </span>
                            <span className="text-sm text-primary-400 font-semibold">
                              / month
                            </span>
                          </div>
                          <button
                            onClick={handleEditLimit}
                            className="px-3 py-1 text-sm bg-primary-100 text-primary-500 hover:bg-primary-200 rounded-md font-medium transition-colors"
                          >
                            ✏️ Edit
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-white rounded-lg p-3 shadow-md space-y-3">
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-primary-500">
                            $
                          </span>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={tempLimit}
                            onChange={(e) => setTempLimit(e.target.value)}
                            className="flex-1 px-3 py-2 text-lg font-bold border-2 border-primary-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                            placeholder="0.00"
                          />
                          <span className="text-sm text-primary-400 font-semibold">
                            / month
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <button
                            onClick={handleSaveLimit}
                            className="flex-1 px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 font-semibold transition-colors"
                          >
                            ✅ Save
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="flex-1 px-4 py-2 text-sm border-2 border-gray-300 text-gray-600 rounded-md hover:bg-gray-50 font-semibold transition-colors"
                          >
                            ❌ Cancel
                          </button>
                        </div>
                      </div>
                    )}

                    {/* Warning for high limits */}
                    {billingLimit > 500 && (
                      <div className="flex items-center gap-2 mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <AlertCircle className="w-3 h-3 text-yellow-600" />
                        <span className="text-xs text-yellow-800">
                          High billing limit set
                        </span>
                      </div>
                    )}

                    {/* Default indicator */}
                    {billingLimit === 100 && (
                      <div className="mt-2 text-xs text-primary-100">
                        ✓ Default limit applied
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "Settings" && (
              <AgentSettings
                data={data}
                setData={setData}
                settings={settings}
                multiSelectValues={multiSelectValues}
                setMultiSelectValues={setMultiSelectValues}
                agent={agent}
              />
            )}

            {activeTab === "Output" && <OutputSettings />}

            {activeTab === "Usage" && <AgentUsage />}
          </div>
        </>
      )}
    </>
  );
}
