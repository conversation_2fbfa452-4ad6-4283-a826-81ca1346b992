"use client";
import React, {
  Fragment,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { DataContext } from "~/store/GlobalState";
import { groupMessagesByDate } from "~/utils/group-messages";
import InfiniteScroll from "react-infinite-scroll-component";
import Image from "next/image";
import images from "~/assets/images";
import UsePeopleMessage from "../../home/<USER>/hooks/people-message";
import { Button } from "~/components/ui/button";
import Message from "../ChannelMessage/message";
import { ACTIONS } from "~/store/Actions";
import EditMessageBox from "../message-box/edit";
import { useParams } from "next/navigation";
import { GetRequest, PutRequest } from "~/utils/new-request";
import { Badge } from "~/components/ui/badge";
import { ArrowDownIcon } from "lucide-react";

const PeopleMessage = ({ participant }: any) => {
  const { fetchMoreData, hasMore } = UsePeopleMessage();
  const { state, dispatch } = useContext(DataContext);
  const { chats, user, isEdit, thread, notify } = state;
  const groupedMessages = groupMessagesByDate(chats);
  const params = useParams();
  const id = params.id as string;
  const [showBadge, setShowBadge] = useState(false);

  const scrollableContainerRef = useRef<HTMLDivElement>(null);
  const hasDispatchedRef = useRef(false);

  const getData = async () => {
    await GetRequest(`/dms/channels/${id}/threads?page=1&limit=1`);
  };

  useEffect(() => {
    const container = scrollableContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const isAtBottom = container.scrollTop >= 0;

      if (isAtBottom) {
        getData();
        hasDispatchedRef.current = true;
      } else if (!isAtBottom && hasDispatchedRef.current) {
        hasDispatchedRef.current = false;
      }

      setShowBadge(!isAtBottom);
    };

    container.addEventListener("scroll", handleScroll);

    // Trigger initial check
    handleScroll();
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (!showBadge && notify?.user_id !== user?.user_id) {
      getData();
    }
  }, [chats]);

  // edit message
  const handleEditMessage = async (content: any) => {
    // payload
    const payload = {
      content: content,
    };

    await PutRequest(
      `/dms/thread/${thread?.thread_id}/channels/${id}`,
      payload
    );
    dispatch({
      type: ACTIONS.IS_EDIT,
      payload: false,
    });
  };

  //

  return (
    <div
      id="scrollableDivs"
      ref={scrollableContainerRef}
      style={{
        height: "100vh",
        overflowY: "scroll",
        display: "flex",
        flexDirection: "column-reverse",
      }}
      className="w-full pb-40"
    >
      {showBadge && state?.dmCount > 0 && (
        <Badge
          onClick={() => {
            if (scrollableContainerRef.current) {
              scrollableContainerRef.current.scrollTop = 0;
            }
            getData();
          }}
          className="absolute bottom-40 z-20 mx-auto cursor-pointer -translate-x-[50%] left-1/2 px-3 py-1.5 flex gap-1 bg-primary-500 font-normal text-white text-[0.8125rem] border border-[E6EAEF]"
        >
          <ArrowDownIcon />
          Latest messages
        </Badge>
      )}

      <InfiniteScroll
        dataLength={chats?.length}
        next={fetchMoreData}
        hasMore={hasMore}
        loader={
          chats?.length !== 0 && (
            <h4 className="my-5 text-xs text-center">Loading threads...</h4>
          )
        }
        style={{
          display: "flex",
          flexDirection: "column-reverse",
          overflowY: "visible",
        }}
        scrollableTarget="scrollableDivs"
        inverse={true}
      >
        {Object.entries(groupedMessages)?.map(([dateLabel, threads]: any) => (
          <Fragment key={dateLabel}>
            {threads?.map((item: any, index: number) => {
              const isCurrentUser = item.user_id === user?.id;
              const nextMessage = threads[index + 1];
              const shouldShowAvatar =
                !nextMessage || nextMessage.user_id !== item.user_id;

              return (
                <React.Fragment key={index}>
                  {isEdit && thread?.thread_id === item?.thread_id ? (
                    <div
                      className={`flex mb-5 mt-2 z-10 bg-white px-5 py-3 bg-blue-50 w-full`}
                    >
                      <div className="size-10 mb-2 mr-3">
                        <Image
                          src={
                            item?.avatar_url
                              ? item?.avatar_url
                              : item?.user_type == "user" ||
                                  item?.user_type === ""
                                ? images?.user
                                : images?.bot
                          }
                          alt="avatar"
                          width={80}
                          height={40}
                          className="rounded-[7px] border size-10"
                        />
                      </div>

                      <EditMessageBox
                        subscription={state?.chatSubscription}
                        sendMessage={handleEditMessage}
                      />
                    </div>
                  ) : (
                    <Message
                      item={item}
                      shouldShowAvatar={shouldShowAvatar}
                      isCurrentUser={isCurrentUser}
                    />
                  )}
                </React.Fragment>
              );
            })}

            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-dotted border-[#E6EAEF]"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]">
                  {dateLabel}
                </span>
              </div>
            </div>
          </Fragment>
        ))}
      </InfiniteScroll>

      {!hasMore && (
        <div className="mt-auto px-5">
          <div className="flex gap-2 items-center my-4">
            <div className="relative">
              <Image
                key={participant?.id}
                src={participant?.avatar_url || images?.user}
                alt={participant?.name}
                width={82}
                height={82}
                className="rounded-lg"
              />
              <div className="absolute -bottom-1 -right-1 bg-[#00AD51] w-4 h-4 rounded-full border border-white" />
            </div>
            <h3 className="text-lg font-bold text-[#1D2939]">
              {participant?.username}
            </h3>
          </div>
          <p className="text-[17px] text-[#344054] mb-2">
            This conversation is just between you and
            <span className="ml-1 py-[1px] px-[3px] bg-[#F1F1FE] text-[#7141F8] text-[15px] rounded-[3px]">
              {" "}
              @{participant?.username}
            </span>{" "}
            . Check out their profile to learn about them.
          </p>
          <Button
            variant={"outline"}
            className="h-10"
            onClick={() =>
              dispatch({ type: ACTIONS.SHOW_PROFILE, payload: true })
            }
          >
            View Profile
          </Button>
        </div>
      )}
    </div>
  );
};

export default PeopleMessage;
