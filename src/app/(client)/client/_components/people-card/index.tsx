"use client";

import { cn } from "~/lib/utils";
import Image from "next/image";
import { useParams, useRouter } from "next/navigation";
import images from "~/assets/images";
import { formatCount } from "~/utils/utils";

interface ComponentProps {
  username: string;
  participant_id?: string;
  channel_id?: string;
  avatar_url: string;
  channel_type: string;
  thread_count: number;
}

export const PeopleHomeCard = (props: ComponentProps) => {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  const handleRoute = async () => {
    localStorage.setItem("channelName", props?.username);
    if (props?.channel_type === "dm") {
      router.push(
        `/client/home/<USER>/${props?.channel_id}/${props?.participant_id}/dm`
      );
    } else {
      router.push(`/client/home/<USER>/${props?.channel_id}/dms`);
    }
  };

  const isSelected = props.channel_id === id;
  const isActive = props?.thread_count > 0;

  //

  return (
    <li
      className={cn(
        "relative px-2 py-2 mx-2 flex items-center rounded-lg group hover:bg-blue-200 hover:text-white cursor-pointer",
        isSelected ? "bg-blue-200 text-white" : ""
      )}
      onClick={handleRoute}
    >
      <div className="flex-1 flex items-center gap-3">
        {props?.channel_type === "dm" ? (
          <div className="relative flex items-center justify-center size-2 h-4 w-4 rounded bg-blue-100">
            <Image
              src={props?.avatar_url || images?.user}
              alt=""
              width={20}
              height={20}
              className="h-4 w-4 object-cover rounded"
              unoptimized
            />

            <div className="absolute -bottom-1 -right-1 bg-green-500 h-[7px] w-[7px] border border-white rounded-full" />
          </div>
        ) : (
          <div className="relative flex items-center h-4 w-4 rounded">
            <Image
              src={props?.avatar_url || images?.user}
              alt=""
              width={20}
              height={20}
              className="h-4 w-4 object-cover rounded absolute -top-[3px]"
              unoptimized
            />

            <div className="text-[10px] absolute -bottom-1 -right-2 bg-blue-500 h-[15px] w-[15px] rounded text-white flex items-center justify-center">
              {props?.username?.split(", ").length}
            </div>
          </div>
        )}

        <p
          className={cn(
            "text-[15px] leading-4 capitalize truncate w-[180px]",
            isActive ? "font-semibold text-white" : "text-blue-50"
          )}
          title={props.username}
        >
          {props.username}
        </p>
      </div>

      {props.thread_count > 0 && (
        <div
          className={cn(
            `absolute right-3 flex items-center justify-center rounded-full bg-blue-200 hover:bg-blue-500 text-white tracking-[-0.5%] font-bold text-right text-[10px] px-2`,
            isSelected ? "bg-blue-500 text-white" : ""
          )}
        >
          {formatCount(props?.thread_count || 0)}
        </div>
      )}
    </li>
  );
};
