"use client";
import React, {
  Fragment,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { DataContext } from "~/store/GlobalState";
import { groupMessagesByDate } from "~/utils/group-messages";
import InfiniteScroll from "react-infinite-scroll-component";
import Image from "next/image";
import images from "~/assets/images";
import UsePeopleMessage from "../../home/<USER>/hooks/people-message";
import Message from "../ChannelMessage/message";
import { GetRequest } from "~/utils/new-request";
import { useParams } from "next/navigation";
import { Badge } from "~/components/ui/badge";
import { ArrowDownIcon } from "lucide-react";

const GroupMessage = ({ participants }: any) => {
  const { fetchMoreData, hasMore } = UsePeopleMessage();
  const { state } = useContext(DataContext);
  const { chats, user, notify } = state;
  const groupedMessages = groupMessagesByDate(chats);
  const [showBadge, setShowBadge] = useState(false);
  const params = useParams();
  const id = params.id as string;

  const scrollableContainerRef = useRef<HTMLDivElement>(null);
  const hasDispatchedRef = useRef(false);

  const getData = async () => {
    await GetRequest(`/group-dms/channels/${id}/threads?page=1&limit=1`);
  };

  useEffect(() => {
    const container = scrollableContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const isAtBottom = container.scrollTop >= 0;

      if (isAtBottom) {
        getData();
        hasDispatchedRef.current = true;
      } else if (!isAtBottom && hasDispatchedRef.current) {
        hasDispatchedRef.current = false;
      }

      setShowBadge(!isAtBottom);
    };

    container.addEventListener("scroll", handleScroll);

    // Trigger initial check
    handleScroll();
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (!showBadge && notify?.user_id !== user?.user_id) {
      getData();
    }
  }, [chats]);

  //

  return (
    <div
      id="scrollableDivs"
      ref={scrollableContainerRef}
      style={{
        height: "100vh",
        overflowY: "scroll",
        display: "flex",
        flexDirection: "column-reverse",
      }}
      className="w-full pb-40"
    >
      {showBadge && state?.dmCount > 0 && (
        <Badge
          onClick={() => {
            if (scrollableContainerRef.current) {
              scrollableContainerRef.current.scrollTop = 0;
            }
            getData();
          }}
          className="absolute bottom-40 z-20 mx-auto cursor-pointer -translate-x-[50%] left-1/2 px-3 py-1.5 flex gap-1 bg-primary-500 font-normal text-white text-[0.8125rem] border border-[E6EAEF]"
        >
          <ArrowDownIcon />
          Latest messages
        </Badge>
      )}

      <InfiniteScroll
        dataLength={chats?.length}
        next={fetchMoreData}
        hasMore={hasMore}
        loader={
          chats?.length !== 0 && (
            <h4 className="my-5 text-xs text-center">Loading threads...</h4>
          )
        }
        style={{
          display: "flex",
          flexDirection: "column-reverse",
          overflow: "scroll",
        }}
        scrollableTarget="scrollableDivs"
        inverse={true}
      >
        {Object.entries(groupedMessages)?.map(([dateLabel, threads]: any) => (
          <Fragment key={dateLabel}>
            {threads?.map((item: any, index: number) => {
              const isCurrentUser = item.user_id === user?.id;
              const nextMessage = threads[index + 1];
              const shouldShowAvatar =
                !nextMessage || nextMessage.user_id !== item.user_id;

              return (
                <React.Fragment key={index}>
                  <Message
                    item={item}
                    shouldShowAvatar={shouldShowAvatar}
                    isCurrentUser={isCurrentUser}
                  />
                </React.Fragment>
              );
            })}

            <div className="relative my-4">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-dotted border-[#E6EAEF]"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="bg-white px-4 py-1 text-[13px] text-[#101828] border border-[#E6EAEF] rounded-[30px]">
                  {dateLabel}
                </span>
              </div>
            </div>
          </Fragment>
        ))}
      </InfiniteScroll>

      {participants?.length > 0 && (
        <div className="px-5 my-5">
          <div className="flex gap-2 mb-4">
            {participants?.map((user: any) => (
              <Image
                key={user.id}
                src={user.avatar_url || images?.user}
                alt={user.name}
                width={56}
                height={56}
                className="rounded-lg size-16 border"
                unoptimized
              />
            ))}
          </div>
          <p className="text-[17px] text-[#344054] mb-2">
            This is the very beginning of your direct message history with
            {participants?.map((user: any, index: number) => (
              <React.Fragment key={index}>
                <span className="ml-1 py-[1px] px-[3px] bg-[#F1F1FE] text-[#7141F8] text-[15px] rounded-[3px]">
                  {" "}
                  @{user.username}
                </span>
                {index < participants?.length - 2 && ", "}
                {index === participants?.length - 2 && " and"}
              </React.Fragment>
            ))}
          </p>
        </div>
      )}
    </div>
  );
};

export default GroupMessage;
