import React, { useEffect, useState } from "react";
import PdfPreview from "./pdf-preview";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import Prism from "prismjs";
import "prismjs/themes/prism.css";
import "prismjs/components/prism-javascript";
import "prismjs/components/prism-python";
import "prismjs/components/prism-markup";
import "prismjs/components/prism-css";
import ImageViewer from "./image-viewer";

/** @eslint-disable */

interface MediaItem {
  id: string;
  file_name: string;
  file_type: string;
  mime_type: string;
  file_link: string;
}

interface MessageItemProps {
  item: {
    message: string;
    media?: MediaItem[];
    type: string;
  };
}

const MessageItem: React.FC<MessageItemProps> = ({ item }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [image, setImage] = useState<any>(null);

  const trimmedMessage = item.message
    .replace(/\n{2,}/g, "\n\n")
    .replace(/^\n+|\n+$/g, "");

  useEffect(() => {
    if (typeof Prism !== "undefined") {
      Prism.highlightAll();
    }
  }, [trimmedMessage]);

  //

  return (
    <div>
      <div
        style={{
          whiteSpace: "pre-line",
          wordBreak: "break-word",
          overflowWrap: "break-word",
          // lineHeight: "25px",
          // overflowX: "auto",
        }}
        className="text-[#344054] text-[15px] font-[400] whitespace-pre-wrap break-words custom-message"
      >
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          components={{
            a: ({ ...props }) => (
              <a
                {...props}
                style={{ textDecoration: "underline", color: "blue" }}
                target="_blank"
                rel="noopener noreferrer"
              />
            ),
            table: (props) => <table style={{ width: "100%" }} {...props} />,
            th: (props) => (
              <th
                style={{ border: "1px solid #ccc", padding: "8px" }}
                {...props}
              />
            ),
            td: (props) => (
              <td
                style={{ border: "1px solid #ccc", padding: "8px" }}
                {...props}
              />
            ),
            p: (props) => (
              <p
                style={{ color: item?.type === "system" ? "#aaa" : "" }}
                {...props}
              />
            ),
            code: ({ className, children, ...rest }) => {
              const match = /language-(\w+)/.exec(className || "");
              const preProps: React.HTMLAttributes<HTMLPreElement> = {
                className,
                ...rest,
              };
              const codeProps: React.HTMLAttributes<HTMLElement> = {
                // Or HTMLCodeElement if more specific
                className,
                ...rest,
              };

              return match ? (
                <pre {...preProps}>
                  <code {...codeProps}>
                    {String(children).replace(/\n$/, "")}
                  </code>
                </pre>
              ) : (
                <code className={className} {...rest}>
                  {children}
                </code>
              );
            },
          }}
        >
          {trimmedMessage}
        </ReactMarkdown>
      </div>

      {/* Render media images and PDF previews */}
      {item.media && item.media.length > 0 && (
        <div className="mt-2 grid grid-cols-2 md:grid-cols-3 gap-4">
          {item?.media?.map((mediaItem: any) =>
            mediaItem.mime_type.includes("application") ? (
              <PdfPreview key={mediaItem.id} file={mediaItem} />
            ) : (
              <img
                key={mediaItem.id}
                src={mediaItem.file_link}
                alt="image"
                className={`w-full ${item.media && item.media.length > 1 ? "max-h-[200px]" : ""} rounded-md object-cover border cursor-pointer`}
                onClick={() => {
                  setIsOpen(true);
                  setImage(mediaItem);
                }}
              />
            )
          )}
        </div>
      )}

      {isOpen && (
        <ImageViewer
          item={item}
          image={image}
          onClose={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default MessageItem;
