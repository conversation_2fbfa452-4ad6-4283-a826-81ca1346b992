import { useContext } from "react";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Mention from "@tiptap/extension-mention";
import Code from "@tiptap/extension-code";
import CodeBlock from "@tiptap/extension-code-block";
import Link from "@tiptap/extension-link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";

/* eslint-disable */


const UseTextEditor = (subscription?: any) => {
  const { state, dispatch } = useContext(DataContext);
  const name = localStorage.getItem("channelName") || "";


  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          HTMLAttributes: { class: "list-disc pl-5" },
        },
        orderedList: {
          HTMLAttributes: { class: "list-decimal pl-5" },
        },
        listItem: {
          HTMLAttributes: { class: "tracking-wide" },
        },
        paragraph: {
          HTMLAttributes: { class: "tracking-wide" },
        },
      }),
      Placeholder.configure({
        placeholder: `Message #${name}`,
      }),
      Mention.configure({
        HTMLAttributes: {
          class: "mention",
          style:
            "color: blue; font-weight: normal; background:#F1F1FE; padding-left:2px; padding-right:2px",
        },
        suggestion: {
          items: (query) => {
            const queryString = String(query || "").toLowerCase();
            const members =
              state?.orgMembers?.filter((item: any) => {
                let name =
                  item?.name && item?.name !== " " ? item?.name : item?.email;
                return name.toLowerCase().includes(queryString);
              }) ?? [];
            const channelMatch = "@channel".includes(queryString);
            const channel = {
              id: "channel",
              name: "@Channel",
              profile_url: "/images/megaphone.png",
              full_name: "Notify everyone in this channel",
              is_online: false,
            };

            return channelMatch ? [channel, ...members] : members;
          },
          render: () => {
            let component: HTMLElement | null = null;
            let currentHoveredIndex = -1; // Keep track of the currently hovered item index

            const handleKeyDown = (event: any) => {
              if (!component) return;

              const suggestionButtons = Array.from(
                component.querySelectorAll("button")
              );
              if (suggestionButtons.length === 0) return; // No suggestions to navigate

              if (event.key === "ArrowDown") {
                event.preventDefault(); // Prevent default browser scroll
                event.stopPropagation();

                // Remove hover from current if any
                if (
                  currentHoveredIndex !== -1 &&
                  suggestionButtons[currentHoveredIndex]
                ) {
                  suggestionButtons[currentHoveredIndex].classList.remove(
                    "hover"
                  );
                  // Also manually remove text color classes if you had them
                  const currentNameSpan =
                    suggestionButtons[currentHoveredIndex].querySelector(
                      ".name-span"
                    );
                  const currentSecondarySpan =
                    suggestionButtons[currentHoveredIndex].querySelector(
                      ".secondary-span"
                    );
                  if (currentNameSpan)
                    currentNameSpan.classList.remove("text-white");
                  if (currentSecondarySpan)
                    currentSecondarySpan.classList.remove("text-white");
                }

                // Calculate next index with wrap-around
                currentHoveredIndex =
                  (currentHoveredIndex + 1) % suggestionButtons.length;

                const nextButton = suggestionButtons[currentHoveredIndex];
                if (nextButton) {
                  nextButton.classList.add("hover");
                  // Manually add text color classes for the new hovered item
                  const nextNameSpan = nextButton.querySelector(".name-span");
                  const nextSecondarySpan =
                    nextButton.querySelector(".secondary-span");
                  if (nextNameSpan) nextNameSpan.classList.add("text-white");
                  if (nextSecondarySpan)
                    nextSecondarySpan.classList.add("text-white");

                  // Scroll into view
                  nextButton.scrollIntoView({ block: "nearest" });
                }
              } else if (event.key === "ArrowUp") {
                event.preventDefault(); // Prevent default browser scroll
                event.stopPropagation();

                // Remove hover from current if any
                if (
                  currentHoveredIndex !== -1 &&
                  suggestionButtons[currentHoveredIndex]
                ) {
                  suggestionButtons[currentHoveredIndex].classList.remove(
                    "hover"
                  );
                  // Also manually remove text color classes
                  const currentNameSpan =
                    suggestionButtons[currentHoveredIndex].querySelector(
                      ".name-span"
                    );
                  const currentSecondarySpan =
                    suggestionButtons[currentHoveredIndex].querySelector(
                      ".secondary-span"
                    );
                  if (currentNameSpan)
                    currentNameSpan.classList.remove("text-white");
                  if (currentSecondarySpan)
                    currentSecondarySpan.classList.remove("text-white");
                }

                // Calculate previous index with wrap-around
                currentHoveredIndex =
                  (currentHoveredIndex - 1 + suggestionButtons.length) %
                  suggestionButtons.length;

                const prevButton = suggestionButtons[currentHoveredIndex];
                if (prevButton) {
                  prevButton.classList.add("hover");
                  // Manually add text color classes for the new hovered item
                  const prevNameSpan = prevButton.querySelector(".name-span");
                  const prevSecondarySpan =
                    prevButton.querySelector(".secondary-span");
                  if (prevNameSpan) prevNameSpan.classList.add("text-white");
                  if (prevSecondarySpan)
                    prevSecondarySpan.classList.add("text-white");

                  // Scroll into view
                  prevButton.scrollIntoView({ block: "nearest" });
                }
              } else if (event.key === "Enter") {
                const selectedButton = component.querySelector(
                  "button.hover"
                ) as HTMLElement | null;
                if (selectedButton) {
                  event.preventDefault();
                  event.stopPropagation();
                  selectedButton.click();
                  return;
                } else if (suggestionButtons.length === 1) {
                  // If there's only one suggestion and no explicit hover (e.g., just typed enough)
                  event.preventDefault();
                  event.stopPropagation();
                  suggestionButtons[0].click();
                  return;
                }
              } else if (event.key === "Escape") {
                component.remove();
                component = null;
                currentHoveredIndex = -1; // Reset index on exit
                event.preventDefault();
                event.stopPropagation();
              }
            };

            const handleClickOutside = (event: any) => {
              if (component && !component.contains(event.target)) {
                component.remove();
                component = null;
                currentHoveredIndex = -1; // Reset index on exit
              }
            };

            return {
              onStart: ({ query, command, clientRect }) => {
                component = document.createElement("div");
                component.className =
                  "absolute border border-gray-300 rounded-lg shadow-lg bg-[#F9FAFB] overflow-y-auto z-50 w-[350px]";
                document.body.appendChild(component);

                document.addEventListener("keydown", handleKeyDown, true);
                document.addEventListener("mousedown", handleClickOutside);

                populateMentions(component, query, command, clientRect);
                // Initialize hover on the first item if there are any
                const firstButton = component.querySelector("button");
                if (firstButton) {
                  firstButton.classList.add("hover");
                  const firstNameSpan = firstButton.querySelector(".name-span");
                  const firstSecondarySpan =
                    firstButton.querySelector(".secondary-span");
                  if (firstNameSpan) firstNameSpan.classList.add("text-white");
                  if (firstSecondarySpan)
                    firstSecondarySpan.classList.add("text-white");
                  currentHoveredIndex = 0;
                } else {
                  currentHoveredIndex = -1;
                }
              },
              onUpdate: ({ query, command, clientRect }) => {
                if (!component) return;
                component.innerHTML = "";
                populateMentions(component, query, command, clientRect);
                // Reset hover to first item on update if there are items
                const firstButton = component.querySelector("button");
                if (firstButton) {
                  firstButton.classList.add("hover");
                  const firstNameSpan = firstButton.querySelector(".name-span");
                  const firstSecondarySpan =
                    firstButton.querySelector(".secondary-span");
                  if (firstNameSpan) firstNameSpan.classList.add("text-white");
                  if (firstSecondarySpan)
                    firstSecondarySpan.classList.add("text-white");
                  currentHoveredIndex = 0;
                } else {
                  currentHoveredIndex = -1;
                }
              },
              onExit: () => {
                if (component) {
                  component.remove();
                }
                document.removeEventListener("keydown", handleKeyDown, true);
                document.removeEventListener("mousedown", handleClickOutside);
                component = null; // Ensure component is null after removal
                currentHoveredIndex = -1; // Reset index on exit
              },
            };

            function populateMentions(
              component: HTMLElement,
              query: string,
              command: Function,
              clientRect: (() => DOMRect | null) | null | undefined
            ) {
              const queryString = String(query || "").toLowerCase();

              const members =
                state?.orgMembers?.filter((item: any) =>
                  (item.name || item.email).toLowerCase().includes(queryString)
                ) ?? [];

              const includeChannel =
                queryString === "" || "@channel".includes(queryString);

              const channelMentionItem = {
                id: "00000000-0000-0000-0000-000000000000",
                name: "@Channel",
                profile_url: "/images/megaphone.png",
                full_name: "Notify everyone in this channel",
                is_online: false,
                type: "user",
              };

              const filteredItems = includeChannel
                ? [channelMentionItem, ...members]
                : members;

              if (filteredItems.length === 0) {
                if (component) component.style.display = "none";
                return;
              }
              if (component) component.style.display = "";

              const coords =
                typeof clientRect === "function" ? clientRect() : null;
              if (coords && component) {
                const editorTop = coords.top + window.scrollY;
                const itemHeight = 48;
                const calculatedHeight = filteredItems.length * itemHeight;
                const maxHeight = 300;
                const dropdownHeight = Math.min(calculatedHeight, maxHeight);
                const isAbove = editorTop > dropdownHeight + itemHeight;
                const dropdownTop = isAbove
                  ? editorTop - dropdownHeight - coords.height
                  : coords.bottom + window.scrollY;

                Object.assign(component.style, {
                  top: `${dropdownTop}px`,
                  left: `${coords.left + window.scrollX}px`,
                  maxHeight: `${maxHeight}px`,
                });
              }

              filteredItems.forEach((item: any) => {
                const button = document.createElement("button");

                button.className =
                  "group flex items-center px-3 py-2 text-left w-full gap-3";

                // Add mouseover/mouseout to update currentHoveredIndex
                button.addEventListener("mouseover", () => {
                  const buttons = Array.from(
                    component!.querySelectorAll("button")
                  );
                  buttons.forEach((btn, idx) => {
                    if (btn === button) {
                      if (!btn.classList.contains("hover")) {
                        // Only update if not already hovered
                        btn.classList.add("hover");
                        const nameSpan = btn.querySelector(".name-span");
                        const secondarySpan =
                          btn.querySelector(".secondary-span");
                        if (nameSpan) nameSpan.classList.add("text-white");
                        if (secondarySpan)
                          secondarySpan.classList.add("text-white");
                        currentHoveredIndex = idx;
                      }
                    } else {
                      if (btn.classList.contains("hover")) {
                        btn.classList.remove("hover");
                        const nameSpan = btn.querySelector(".name-span");
                        const secondarySpan =
                          btn.querySelector(".secondary-span");
                        if (nameSpan) nameSpan.classList.remove("text-white");
                        if (secondarySpan)
                          secondarySpan.classList.remove("text-white");
                      }
                    }
                  });
                });
                // No mouseout listener needed here if you want keyboard hover to persist on mouse move

                const avatarContainer = document.createElement("div");
                avatarContainer.className =
                  "w-6 h-6 flex items-center justify-center rounded-md bg-gray-200 text-white font-bold text-sm overflow-hidden";
                avatarContainer.style.minWidth = "1.5rem";

                const img = document.createElement("img");
                img.src = item.profile_url || "/images/user.png";
                img.alt = item.name || item.email;
                img.className = "w-full h-full rounded-md object-cover border";
                avatarContainer.appendChild(img);

                const textContainer = document.createElement("div");
                textContainer.className = "flex items-center gap-2";

                const mainTextLine = document.createElement("div");
                mainTextLine.className = "flex items-center gap-2";

                const nameSpan = document.createElement("span");
                nameSpan.textContent = item.name || item.email;

                nameSpan.className =
                  "text-sm font-bold capitalize text-gray-800 name-span";

                const status = document.createElement("div");
                if (item.id !== "channel") {
                  status.className = `size-2 rounded-full ${
                    item.is_online ? "bg-green-500" : "bg-gray-400"
                  }`;
                }

                mainTextLine.appendChild(nameSpan);
                if (item.id !== "channel") mainTextLine.appendChild(status);

                const secondaryTextSpan = document.createElement("span");
                secondaryTextSpan.textContent =
                  item?.id === "00000000-0000-0000-0000-000000000000"
                    ? item.full_name
                    : item.name &&
                        item.email &&
                        item.name.toLowerCase() !== item.email.toLowerCase()
                      ? item.email
                      : "";

                secondaryTextSpan.className =
                  "text-xs text-gray-500 secondary-span";

                textContainer.appendChild(mainTextLine);
                if (secondaryTextSpan.textContent) {
                  textContainer.appendChild(secondaryTextSpan);
                }

                button.appendChild(avatarContainer);
                button.appendChild(textContainer);

                button.onclick = () => {
                  let labelText = item.name;
                  if (!labelText || labelText.trim() === "") {
                    labelText = item.email;
                  }
                  const finalLabel = labelText.replace(/^@/, "");
                  const mentionCommandPayload = {
                    id: item.id,
                    label: finalLabel,
                  };

                  if (item.id !== "channel") {
                    const mentionForDispatch = {
                      id: item.id,
                      label: finalLabel,
                      type: "user",
                    };
                    if (
                      !state.mentions.some(
                        (m: any) => m.id === mentionForDispatch.id
                      )
                    ) {
                      dispatch({
                        type: ACTIONS.MENTIONS,
                        payload: [mentionForDispatch],
                      });
                    }
                  }
                  command(mentionCommandPayload);
                };
                component.appendChild(button);
              });
            }
          },
        },
      }),
      Link.configure({
        openOnClick: true,
        linkOnPaste: true,
        autolink: true,
        defaultProtocol: "https",
        HTMLAttributes: {
          class: "text-primary-500",
        },
      }),
      Code,
      CodeBlock.configure({
        HTMLAttributes: {
          class: "language-javascript",
        },
      }),
    ],
    // onUpdate: () => {
    //   handleTyping(true);
    // },
    // onBlur: () => handleTyping(false),
  });

  return {
    editor,
  };
};

export default UseTextEditor;
