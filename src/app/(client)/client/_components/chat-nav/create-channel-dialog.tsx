"use client";
import React, { useContext, useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { CircleAlert } from "lucide-react";
import Image from "next/image";
import { Checkbox } from "~/components/ui/checkbox";
import { useRouter } from "next/navigation";
import { PostRequest } from "~/utils/new-request";
import { ACTIONS } from "~/store/Actions";
import { DataContext } from "~/store/GlobalState";
import Loading from "~/components/ui/loading";
import images from "~/assets/images";

interface CreateChannelDialogProps {
  children: React.ReactNode;
}

const CreateChannelDialog = ({ children }: CreateChannelDialogProps) => {
  const [channelName, setChannelName] = useState("");
  const [step, setStep] = useState(1);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const { state, dispatch } = useContext(DataContext);
  const { orgId, orgMembers: users, user } = state;
  const [loading, setLoading] = useState(false);
  const [visibility, setVisibility] = useState<"public" | "private">("public");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 1) {
      setStep(2);
    } else {
      createChannel(e);
    }
  };

  const handleUserToggle = (userId: string) => {
    if (userId === "all") {
      if (selectedUsers?.length === users?.length) {
        setSelectedUsers([]);
      } else {
        setSelectedUsers(users?.map((user: any) => user?.id));
      }
    } else {
      setSelectedUsers((prev) =>
        prev.includes(userId)
          ? prev.filter((id) => id !== userId)
          : [...prev, userId]
      );
    }
  };

  const createChannel = async (e: any) => {
    e.preventDefault();

    setLoading(true);

    const payload = {
      name: channelName,
      organisation_id: orgId,
      is_private: visibility === "private" ? true : false,
      Username: user?.username || user?.email,
    };

    const res = await PostRequest(`/channels`, payload);

    if (res?.status === 200 || res?.status === 201) {
      dispatch({
        type: ACTIONS.CHANNEL_CALLBACK,
        payload: !state?.channelCallback,
      });

      const channelId = res?.data?.data?.channels_id;
      localStorage.setItem("channelName", res?.data?.data?.name);
      localStorage.setItem("channelId", channelId);

      const newPayload = {
        channel_id: channelId,
        user_ids: selectedUsers,
      };

      const newRes = await PostRequest(`/channels/add-multiple`, newPayload);

      if (newRes?.status === 200 || newRes?.status === 201) {
        router.push(`/client/home/<USER>/${channelId}`);
        setOpen(false);
        setStep(1);
        setChannelName("");
        setSelectedUsers([]);
      }
    }

    setLoading(false);
  };

  //

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      <DialogContent className="sm:max-w-[1089px] p-0 m-0 flex h-[90vh] gap-0 overflow-hidden">
        <div className="w-[381px] flex flex-col gap-5 border-r border-[#E6EAEF]">
          <div className="py-5 px-6 border-b border-[#E6EAEF]">
            <h2 className="text-[#101828] font-black text-xl">
              {step === 1 ? "Create a Channel" : "Choose Your Members"}
            </h2>
          </div>

          <div className="flex-1 overflow-y-auto px-6">
            <form onSubmit={handleSubmit}>
              {step === 1 ? (
                <>
                  <div className="space-y-2 mb-6">
                    <label
                      htmlFor="name"
                      className="block text-[#344054] text-sm font-medium"
                    >
                      Channel name
                    </label>
                    <div className="relative">
                      <Input
                        id="name"
                        value={channelName}
                        onChange={(e) =>
                          setChannelName(
                            e.target.value.replace(/\s/g, "-").toLowerCase()
                          )
                        }
                        placeholder="e.g. project-x"
                        className="text-[15px] pr-7"
                        maxLength={40}
                      />
                      <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-gray-400">
                        {40 - channelName.length}
                      </span>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-[#101828] text-[17px] font-semibold mb-2">
                      Channel Visibility
                    </h3>

                    <RadioGroup
                      value={visibility}
                      onValueChange={(value) =>
                        setVisibility(value as "public" | "private")
                      }
                      className="flex flex-col gap-3"
                    >
                      {/* Public */}
                      <div className="flex items-start gap-3">
                        <RadioGroupItem
                          value="public"
                          id="public"
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <label
                            htmlFor="public"
                            className="text-[15px] text-[#0A0A0A]"
                          >
                            Public - Anyone in {state?.orgData?.name}
                          </label>
                        </div>
                      </div>

                      {/* Private */}
                      <div className="flex items-start gap-3">
                        <RadioGroupItem
                          value="private"
                          id="private"
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <label
                            htmlFor="private"
                            className="text-[15px] text-[#0A0A0A]"
                          >
                            Private - Only specific people{" "}
                            <span className="text-[#667085]">
                              (can only be viewed or joined by invitation)
                            </span>
                          </label>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </>
              ) : (
                <>
                  <h4 className="text-[#475467] text-[15px] mb-5">
                    Which group chat members would you like to add to this
                    channel?
                  </h4>
                  <div className="flex items-center gap-[10px] mb-6">
                    <Checkbox
                      id={"all"}
                      checked={selectedUsers?.length === users?.length}
                      onCheckedChange={() => handleUserToggle("all")}
                      className="rounded-sm w-5 h-5"
                    />
                    <div className="relative w-9 h-9">
                      <Image
                        width={24}
                        height={24}
                        src={users[0].profile_url || images?.user}
                        className="rounded-[7px]"
                        alt={"user"}
                      />
                      <span className="absolute right-0 -bottom-1 bg-[#4B4BBF] text-xs px-1 w-6 h-6 rounded-[3px] text-[#F1F1FE] flex items-center justify-center">
                        {users?.length}
                      </span>
                    </div>
                    <p className="text-[15px] text-[#101828] font-medium">
                      Select all
                    </p>
                  </div>

                  <div className="space-y-6">
                    {users?.map((user: any) => (
                      <div
                        key={user.id}
                        className="flex items-center gap-[10px]"
                      >
                        <Checkbox
                          id={user.id}
                          checked={selectedUsers?.includes(user?.id)}
                          onCheckedChange={() => handleUserToggle(user?.id)}
                          className="rounded-sm w-5 h-5"
                        />
                        <Image
                          src={user.profile_url || images?.user}
                          alt={user.name}
                          width={37}
                          height={37}
                          className="rounded-[7px] size-8"
                        />
                        <div className="flex items-center gap-1">
                          <label
                            htmlFor={user.id}
                            className="text-[15px] text-[#101828] font-medium"
                          >
                            @{user.name}
                          </label>
                          <div className="w-[6px] h-[6px] bg-[#E6EAEF] rounded-full" />
                          <span className="text-[13px] text-[#475467]">
                            {user.name}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </form>
          </div>

          <div className="mt-6 mx-6 bg-[#F9FAFB] border border-[#F2F4F7] rounded-[7px] flex items-start gap-[6px] py-2 px-3">
            <CircleAlert size={20} color="#475467" />
            <p className="text-sm text-[#344054]">
              {step === 1
                ? "This workspace only allows you to create public channels."
                : "All your messages are automatically copied to your channel from your group chat."}
            </p>
          </div>

          <div className="flex gap-4 py-5 px-6 mt-auto border-t border-[#E6EAEF]">
            {step === 1 ? (
              <Button
                variant="outline"
                type="button"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
            ) : (
              <Button
                onClick={() => setStep(1)}
                variant="outline"
                type="button"
              >
                Back
              </Button>
            )}
            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={
                step === 1
                  ? channelName.length === 0
                  : selectedUsers?.length === 0
              }
              className="gap-1 bg-[#7141F9] hover:bg-[#5B2BF0] text-white w-full"
            >
              {step === 1 ? "Next" : "Create Channel"} {loading && <Loading />}
            </Button>
          </div>
        </div>

        <div className="flex-1 bg-[#F1F1FE] relative hidden md:flex items-center justify-center p-5 pb-10">
          <Image
            src={images?.createChannel}
            alt="Create channel illustration"
            // fill
            // className="object-contain"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateChannelDialog;
