"use client";
import React from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import SettingsNavigation from "~/components/settings/SettingsNavigation";
import { 
  DollarSign, 
  Users, 
  Shield, 
  Bell, 
  ArrowRight,
  TrendingUp,
  AlertTriangle
} from "lucide-react";

const SettingsPage: React.FC = () => {
  const quickActions = [
    {
      title: "Spending Limits",
      description: "Configure AI and billing spending limits",
      href: "/dashboard/settings/spending-limits",
      icon: DollarSign,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      urgent: true
    },
    {
      title: "User Management",
      description: "Manage team members and permissions",
      href: "/dashboard/settings/users",
      icon: Users,
      color: "text-green-500",
      bgColor: "bg-green-50",
      urgent: false
    },
    {
      title: "Security Settings",
      description: "Configure security and access controls",
      href: "/dashboard/settings/security",
      icon: Shield,
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      urgent: false
    },
    {
      title: "Notifications",
      description: "Set up alerts and notifications",
      href: "/dashboard/settings/notifications",
      icon: Bell,
      color: "text-yellow-500",
      bgColor: "bg-yellow-50",
      urgent: false
    }
  ];

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-1">
            Manage your organization settings and preferences
          </p>
        </div>
      </div>

      {/* Alert for spending limits */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600" />
          <div className="flex-1">
            <h3 className="font-medium text-yellow-800">
              Configure Spending Limits
            </h3>
            <p className="text-sm text-yellow-700 mt-1">
              Set up global spending limits for AI calls and user billing to control costs and prevent overages.
            </p>
          </div>
          <Link href="/dashboard/settings/spending-limits">
            <Button className="bg-yellow-600 hover:bg-yellow-700 text-white">
              Configure Now
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Navigation Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Settings Menu</CardTitle>
              <CardDescription>
                Navigate to different settings sections
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <SettingsNavigation />
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-blue-500" />
                Quick Actions
              </CardTitle>
              <CardDescription>
                Common settings and configurations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <Link key={action.href} href={action.href}>
                      <div className={`p-4 rounded-lg border-2 border-transparent hover:border-gray-200 transition-all cursor-pointer group ${action.bgColor}`}>
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg bg-white shadow-sm`}>
                              <Icon className={`w-5 h-5 ${action.color}`} />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 group-hover:text-gray-700">
                                {action.title}
                                {action.urgent && (
                                  <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                                    Recommended
                                  </span>
                                )}
                              </h4>
                              <p className="text-sm text-gray-600 mt-1">
                                {action.description}
                              </p>
                            </div>
                          </div>
                          <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Settings Changes</CardTitle>
              <CardDescription>
                Latest configuration updates and changes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      Spending limits feature added
                    </p>
                    <p className="text-xs text-gray-500">
                      Configure global spending limits for better cost control
                    </p>
                  </div>
                  <span className="text-xs text-gray-500">Just now</span>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      Organization settings updated
                    </p>
                    <p className="text-xs text-gray-500">
                      Basic organization information configured
                    </p>
                  </div>
                  <span className="text-xs text-gray-500">2 hours ago</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
              <CardDescription>
                Current system health and configuration status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">✓</div>
                  <div className="text-sm font-medium text-green-800">Security</div>
                  <div className="text-xs text-green-600">All systems secure</div>
                </div>
                
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">⚠</div>
                  <div className="text-sm font-medium text-yellow-800">Spending Limits</div>
                  <div className="text-xs text-yellow-600">Configuration needed</div>
                </div>
                
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">ℹ</div>
                  <div className="text-sm font-medium text-blue-800">Integrations</div>
                  <div className="text-xs text-blue-600">Ready for setup</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
