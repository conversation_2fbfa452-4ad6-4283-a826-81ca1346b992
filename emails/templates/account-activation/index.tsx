import {
  Container,
  Heading,
  Preview,
  Section,
  Text,
  Button,
  Row,
  Column,
} from "@react-email/components";
import React from "react";
import Layout from "../../layout/layout";
import { Globe, Navigation, Laptop, Clock4, AtSign } from "lucide-react";

interface AccountActivationProperties {
  username: string;
  link: string;
}

const AccountActivation = ({ username }: AccountActivationProperties) => {
  return (
    <Layout>
      <Container style={containerStyle}>
        <Preview>{username}, activate your account</Preview>
        <Section style={sectionStyle}>
          <Section style={textSectionStyle}>
            <Heading as="h5" style={headingStyle}>
              Activate Your Account
            </Heading>

            <Section>
              <Text style={greetingTextStyle}>Hi {username},</Text>
              <Text style={descriptionFirstTextStyle}>
                We recently detected a login attempt on your account from an
                unfamiliar device. Please review the details:
              </Text>
            </Section>
            <Section style={boxContainerStyle}>
              <Row style={detailsRow}>
                <Column>
                  <AtSign style={iconStyle} />
                </Column>
                <Column style={columnDetailsStyle}>
                  <Text style={descriptionTitleText}>Account</Text>
                  <Text style={detailsValueText}><EMAIL></Text>
                </Column>
              </Row>
              <Row style={detailsRow}>
                <Column>
                  <Navigation style={iconStyle} />
                </Column>
                <Column style={columnDetailsStyle}>
                  <Text style={descriptionTitleText}>
                    IP & approximate location
                  </Text>
                  <Text style={descriptionValueText}>
                    ************** - Lagos, NG-LA, Nigeria
                  </Text>
                </Column>
              </Row>
              <Row style={detailsRow}>
                <Column>
                  <Laptop style={iconStyle} />
                </Column>
                <Column style={columnDetailsStyle}>
                  <Text style={descriptionTitleText}>Device type</Text>
                  <Text style={descriptionValueText}>Mac</Text>
                </Column>
              </Row>
              <Row style={detailsRow}>
                <Column>
                  <Clock4 style={iconStyle} />
                </Column>
                <Column style={columnDetailsStyle}>
                  <Text style={descriptionTitleText}>Time</Text>
                  <Text style={descriptionValueText}>17/09/2024</Text>
                </Column>
              </Row>
              <Row style={lastDetailsRow}>
                <Column>
                  <Globe style={iconStyle} />
                </Column>
                <Column style={columnDetailsStyle}>
                  <Text style={descriptionTitleText}>Application</Text>
                  <Text style={descriptionValueText}>Telex for web</Text>
                </Column>
              </Row>
            </Section>
            <Text style={descriptionTextStyle}>
              If this was not you, reset your password to protect your account.
              To activate your Telex account, please click the button below:
            </Text>
            <div style={{ display: "flex", justifyContent: "center" }}>
              <Button
                href="https://example.com"
                style={{
                  background: "#8860f8",
                  color: "#fff",
                  padding: "12px 20px",
                  borderRadius: "4px",
                }}
              >
                Activate Account
              </Button>
            </div>

            <Section style={footerSectionStyle}>
              <Text style={footerTextStyle}>
                Regards,
                <br />
                The Telex Team
              </Text>
            </Section>
          </Section>
        </Section>
      </Container>
    </Layout>
  );
};

const sectionStyle: React.CSSProperties = {
  margin: "56px 0",
};

const boxContainerStyle: React.CSSProperties = {
  background: "#F7F7F7",
  borderRadius: "12px",
  marginBottom: "30px",
  border: "1px solid #D6D6D6",
};

const columnDetailsStyle: React.CSSProperties = {
  padding: "12px 18px",
};

const iconStyle: React.CSSProperties = {
  padding: "0 0 0 12px",
};

const detailsRow: React.CSSProperties = {
  display: "flex",
  alignItems: "center",
  justifyContent: "start",
  borderBottom: "1px solid #D6D6D6",
};

const lastDetailsRow: React.CSSProperties = {
  display: "flex",
  alignItems: "center",
  justifyContent: "start",
};

const containerStyle: React.CSSProperties = {
  padding: "0 32px",
  maxWidth: "680px",
  margin: "0 auto",
};

const textSectionStyle: React.CSSProperties = {
  marginTop: "16px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
};

const headingStyle: React.CSSProperties = {
  margin: "0 0 56px 0",
  textAlign: "center",
  fontSize: "24px",
  lineHeight: "28px",
  color: "#121212",
};

const greetingTextStyle: React.CSSProperties = {
  fontSize: "16px",
  fontWeight: "600",
  color: "#121212",
  margin: "0 0 32px 0",
};

const descriptionTextStyle: React.CSSProperties = {
  fontSize: "16px",
  color: "#525252",
  lineHeight: "19.36px",
  textAlign: "justify",
  margin: "0 0 16px 0",
};

const descriptionTitleText: React.CSSProperties = {
  fontSize: "16px",
  fontWeight: "500",
  color: "#1D2939",
};

const descriptionValueText: React.CSSProperties = {
  fontSize: "16px",
  color: "#1D2939",
  textAlign: "justify",
  marginTop: "-10px",
};

const detailsValueText: React.CSSProperties = {
  fontSize: "16px",
  color: "#8860f8",
  textAlign: "justify",
  textDecoration: "underline",
  marginTop: "-10px",
};

const descriptionFirstTextStyle: React.CSSProperties = {
  fontSize: "16px",
  color: "#525252",
  lineHeight: "24px",
  textAlign: "justify",
  margin: "0 0 16px 0",
};

const footerSectionStyle: React.CSSProperties = {
  marginTop: "28px",
};

const footerTextStyle: React.CSSProperties = {
  fontWeight: "600",
  color: "#121212",
};

AccountActivation.PreviewProps = {
  username: "Denver",
  link: "www.telex.com",
} satisfies AccountActivationProperties;

export default AccountActivation;
