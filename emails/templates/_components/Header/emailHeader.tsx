import React from "react";
import { Column, Container, Img, Row } from "@react-email/components";

const EmailHeader = () => (
  <Container style={headerContainerStyle}>
    <Row style={headerRowStyle}>
      <Column align="center" style={headerColumnStyle}>
        <Img
          src={"https://i.imgur.com/zn4jQVZ.png"}
          alt="telex logo"
          height="30px"
          width="100px"
          style={imgStyle}
        />
      </Column>
    </Row>
  </Container>
);

const headerContainerStyle = {
  backgroundColor: "#E3D9FE",
  border: "1px solid #E1D6D666",
  padding: "10px",
  textAlign: "center",
  maxWidth: "792px",
  height: "122px",
  margin: "0 auto",
} as React.CSSProperties;

const headerRowStyle: React.CSSProperties = {
  width: "fit-content",
  display: "flex",
  flexDirection: "column" as const,
  alignItems: "center" as const,
  justifyContent: "center" as const,
  gap: "10px",
};

const headerColumnStyle: React.CSSProperties = {
  display: "flex",
  justifyContent: "center" as const,
  alignItems: "center" as const,
  height: "fit-content",
};

const imgStyle: React.CSSProperties = {
  display: "inline-block",
  marginRight: "10px",
};

export default EmailHeader;
