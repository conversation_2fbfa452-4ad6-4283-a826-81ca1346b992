import React from "react";
import {
  Column,
  Container,
  Hr,
  Img,
  <PERSON>,
  Row,
  Section,
  Text,
} from "@react-email/components";

const EmailFooter = () => (
  <Container style={footerContainer}>
    <Section>
      <Row style={socialLinksRow}>
        <Column align="center">
          <Link href="https://staging.telex.im/" aria-label="Twitter">
            <Img
              src="https://imgur.com/TKobTGb.png"
              alt="Twitter"
              style={socialIcon}
            />
          </Link>
        </Column>
        <Column align="center">
          <Link href="https://staging.telex.im/" aria-label="Instagram">
            <Img
              src="https://imgur.com/FYp0z5Q.png"
              alt="Instagram"
              style={socialIcon}
            />
          </Link>
        </Column>
        <Column align="center">
          <Link href="https://staging.telex.im/" aria-label="TikTok">
            <Img
              src="https://imgur.com/tekz4ix.png"
              alt="TikTok"
              style={socialIcon}
            />
          </Link>
        </Column>
        <Column align="center">
          <Link href="https://staging.telex.im/" aria-label="Reddit">
            <Img
              src="https://imgur.com/TeEDDcy.png"
              alt="Reddit"
              style={socialIcon}
            />
          </Link>
        </Column>
        <Column align="center">
          <Link href="https://staging.telex.im/" aria-label="LinkedIn">
            <Img
              src="https://imgur.com/a45KkgZ.png"
              alt="LinkedIn"
              style={socialIcon}
            />
          </Link>
        </Column>
      </Row>
    </Section>

    <Section>
      <Text style={paragraph}>
        Thank you for choosing Telex. Need help?{" "}
        <Link href="https://staging.telex.im/" style={link}>
          Contact our customer support
        </Link>
      </Text>
      <Hr style={divider} />
    </Section>

    <Section>
      <Text style={paragraph}>
        You are receiving this email because you signed up at{" "}
        <Link href="https://staging.telex.im/" style={link}>
          Telex.com
        </Link>
        . Want to change how you receive these emails?
      </Text>
      <Text style={paragraph}>
        You can{" "}
        <Link href="https://staging.telex.im/" style={linkBold}>
          update your preferences
        </Link>{" "}
        or{" "}
        <Link href="https://staging.telex.im/" style={linkBold}>
          unsubscribe from this list.
        </Link>
      </Text>
    </Section>
  </Container>
);

const footerContainer: React.CSSProperties = {
  backgroundColor: "#F9FAFB",
  padding: "32px",
  textAlign: "left",
  fontSize: "14px",
  color: "#5B5B5D",
  maxWidth: "792px",
  margin: "0 auto",
  overflowX: "hidden",
};

const socialLinksRow: React.CSSProperties = {
  width: "fit-content",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  flexWrap: "wrap",
  marginBlock: "32px",
};

const socialIcon: React.CSSProperties = {
  width: "24px",
  height: "24px",
  margin: "0 15px", // Reduce margin for smaller screens
};

const paragraph: React.CSSProperties = {
  margin: "0 0 16px 0",
  lineHeight: "1.6",
};

const link: React.CSSProperties = {
  color: "#5B5B5D",
  textDecoration: "underline",
};

const linkBold: React.CSSProperties = {
  color: "#5B5B5D",
  fontWeight: "bold",
  textDecoration: "underline",
};

const divider: React.CSSProperties = {
  border: "0",
  borderTop: "1px dashed #5B5B5D20",
  margin: "30px 0",
};

export default EmailFooter;
