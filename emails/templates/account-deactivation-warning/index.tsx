import {
  Container,
  Heading,
  Img,
  Preview,
  Section,
  Text,
  Button,
} from "@react-email/components";
import React from "react";

import Layout from "../../layout/layout";

interface AccountDeactivationWarningProperties {
  username: string;
  link: string;
}

const AccountDeactivationWarning = ({
  username,
}: AccountDeactivationWarningProperties) => {
  return (
    <Layout>
      <Container style={containerStyle}>
        <Preview>{username}, your account will be deactivated soon</Preview>
        <Section style={sectionStyle}>
          <Section style={imageContainerStyle}>
            <Img
              src={"https://i.imgur.com/sFKIEzW.png"}
              alt="deactivation-warning"
              width="130px"
              height="130px"
            />
          </Section>

          <Section style={textSectionStyle}>
            <Heading as="h5" style={headingStyle}>
              Account Deactivation In{" "}
              <span style={warningTextStyle}>Two Days!</span>
            </Heading>

            <Section>
              <Text style={greetingTextStyle}>Hi {username},</Text>
              <Text style={descriptionTextStyle}>
                We hope this email finds you well. We noticed that you
                haven&apos;t logged into your Telex account for some time. As
                part of our ongoing efforts to maintain a secure and efficient
                service, we will be deactivating your accounts in two days.
              </Text>
              <Text style={boldTextStyle}>Your deactivation details:</Text>
              <ul style={listStyle}>
                <li style={itemListStyle}>
                  Email:{" "}
                  <span style={listDescriptionTextStyle}><EMAIL></span>
                </li>
                <li style={itemListStyle}>
                  Last Active:{" "}
                  <span style={listDescriptionTextStyle}>
                    17th June 2024/ 11:55pm
                  </span>
                </li>
                <li style={itemListStyle}>
                  Deactivation Date:{" "}
                  <span style={listDescriptionTextStyle}>
                    20th July 2024/ 11:55pm
                  </span>
                </li>
              </ul>
              <Text style={descriptionTextStyle}>
                To keep your account active, simply log in before the
                deactivation date. However, if you no longer wish to use your
                account, no further action is needed.
              </Text>
            </Section>

            <div style={{ display: "flex", justifyContent: "center" }}>
              <Button
                href="https://example.com"
                style={{
                  background: "#8860f8",
                  color: "#fff",
                  padding: "12px 20px",
                  borderRadius: "4px",
                }}
              >
                Access My Account
              </Button>
            </div>

            <Section style={footerSectionStyle}>
              <Text style={footerTextStyle}>
                Regards,
                <br />
                The Telex Team
              </Text>
            </Section>
          </Section>
        </Section>
      </Container>
    </Layout>
  );
};

const sectionStyle: React.CSSProperties = {
  margin: "56px 0",
};

const warningTextStyle: React.CSSProperties = {
  color: "red",
};

const listDescriptionTextStyle: React.CSSProperties = {
  fontWeight: "400",
};

const boldTextStyle: React.CSSProperties = {
  fontWeight: "600",
  fontSize: "16px",
};

const listStyle: React.CSSProperties = {
  marginBottom: "30px",
};

const itemListStyle: React.CSSProperties = {
  marginBottom: "10px",
  fontWeight: "600",
  fontSize: "15px",
};

const imageContainerStyle: React.CSSProperties = {
  margin: "0 auto",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  maxWidth: "fit-content",
};

const containerStyle: React.CSSProperties = {
  padding: "0 32px",
  maxWidth: "680px",
  margin: "0 auto",
};

const textSectionStyle: React.CSSProperties = {
  marginTop: "56px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
};

const headingStyle: React.CSSProperties = {
  margin: "0 0 56px 0",
  textAlign: "center",
  fontSize: "24px",
  lineHeight: "28px",
  color: "#121212",
};

const greetingTextStyle: React.CSSProperties = {
  fontSize: "16px",
  fontWeight: "600",
  color: "#121212",
  margin: "0 0 32px 0",
};

const descriptionTextStyle: React.CSSProperties = {
  fontSize: "16px",
  color: "#525252",
  lineHeight: "19.36px",
  textAlign: "justify",
  margin: "0 0 16px 0",
};

const footerSectionStyle: React.CSSProperties = {
  marginTop: "28px",
};

const footerTextStyle: React.CSSProperties = {
  fontWeight: "600",
  color: "#121212",
};

AccountDeactivationWarning.PreviewProps = {
  username: "Denver",
  link: "www.telex.com",
} satisfies AccountDeactivationWarningProperties;

export default AccountDeactivationWarning;
